%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1864490332, guid: 66bd02e2746234647a685703c57241ec, type: 3}
  m_Name: ObfuscatorOptions
  m_EditorClassIdentifier:
  classes: 1
  methods: 1
  parameters: 1
  fields: 1
  properties: 1
  events: 1
  skipRenameOfAllPublicMonobehaviourMethods: 1
  skipRenameOfAllPublicMonobehaviourFields: 1
  searchForUnityReflectionMethods: 1
  obfuscateLiterals: 1
  stripLiteralEscapeCharacters: 1
  obfuscateMarkerInDecimal: 94
  RSAKeyLength: 1024
  addFakeCode: 0
  minFalseMethodsPerClass: 10
  maxFalseMethodsPerClass: 150
  unicodeStartInDecimal: 65
  numberOfCharacters: 2
  createNameTranslationFile: 0
  nameTranslationFile: nameTranslation.txt
  usePreviousObfuscation: 0
  previousNameTranslationFile: nameTranslation.txt
  obfuscateAndReplaceLiteralsForRPCMethods: 0
  addObfuscatorMarker: 1
  skipNamespaces:
  - Google
  - GooglePlayGames
  - Net.Json
  - HutongGames.PlayMaker
  - ChartboostSDK
  - BestHTTP
  - InControl
  - PlayFab
  - GoogleMobileAds
  - ExitGames
  - Com.Google
  - Zenject
  - UniRx
  - DG.Tweening
  - LeanplumSDK
  - Org.BouncyCastle
  - Bolt
  - BoltInternal
  - UdpKit
  - LapinerTools
  - VRInteraction
  - Photon
  - TMPro
  - QFSW
  - Sirenix
  unityMethods:
  - Awake
  - FixedUpdate
  - LateUpdate
  - OnAnimatorIK
  - OnAnimatorMove
  - OnApplicationFocus
  - OnApplicationPause
  - OnApplicationQuit
  - OnAudioFilterRead
  - OnBecameInvisible
  - OnBecameVisible
  - OnCollisionEnter
  - OnCollisionEnter2D
  - OnCollisionExit
  - OnCollisionExit2D
  - OnCollisionStay
  - OnCollisionStay2D
  - OnConnectedToServer
  - OnControllerColliderHit
  - OnDestroy
  - OnDisable
  - OnDisconnectedFromServer
  - OnDrawGizmos
  - OnDrawGizmosSelected
  - OnEnable
  - OnFailedToConnect
  - OnFailedToConnectToMasterServer
  - OnGUI
  - OnJointBreak
  - OnLevelWasLoaded
  - OnMasterServerEvent
  - OnMouseDown
  - OnMouseDrag
  - OnMouseEnter
  - OnMouseExit
  - OnMouseOver
  - OnMouseUp
  - OnMouseUpAsButton
  - OnNetworkInstantiate
  - OnParticleCollision
  - OnPlayerConnected
  - OnPlayerDisconnected
  - OnPostRender
  - OnPreCull
  - OnPreRender
  - OnRenderImage
  - OnRenderObject
  - OnSerializeNetworkView
  - OnServerInitialized
  - OnTransformChildrenChanged
  - OnTransformParentChanged
  - OnTriggerEnter
  - OnTriggerEnter2D
  - OnTriggerExit
  - OnTriggerExit2D
  - OnTriggerStay
  - OnTriggerStay2D
  - OnValidate
  - OnWillRenderObject
  - Reset
  - Start
  - Update
  - OnConnectedToPhoton
  - OnLeftRoom
  - OnMasterClientSwitched
  - OnPhotonCreateRoomFailed
  - OnPhotonJoinRoomFailed
  - OnCreatedRoom
  - OnJoinedLobby
  - OnLeftLobby
  - OnDisconnectedFromPhoton
  - OnConnectionFail
  - OnFailedToConnectToPhoton
  - OnReceivedRoomListUpdate
  - OnJoinedRoom
  - OnPhotonPlayerConnected
  - OnPhotonPlayerDisconnected
  - OnPhotonRandomJoinFailed
  - OnConnectedToMaster
  - OnPhotonSerializeView
  - OnPhotonInstantiate
  - OnPhotonMaxCccuReached
  - OnPhotonCustomRoomPropertiesChanged
  - OnPhotonPlayerPropertiesChanged
  - OnUpdatedFriendList
  - OnCustomAuthenticationFailed
  - OnCustomAuthenticationResponse
  - OnWebRpcResponse
  - OnOwnershipRequest
  - OnLobbyStatisticsUpdate
  - OnJointBreak2D
  - OnParticleSystemStopped
  - OnParticleTrigger
  - OnParticleUpdateJobScheduled
  skipClasses:
  - MonoPInvokeCallbackAttribute
  - CloudRegionCode
  - PhotonNetworkingMessage
  - PhotonLogLevel
  - PhotonTargets
  - CloudRegionFlag
  - ConnectionState
  - EncryptionMode
  - EncryptionDataParameters
  - ClientState
  - ClientState/JoinType
  - DisconnectCause
  - ServerConnection
  - MatchmakingMode
  - JoinMode
  - ReceiverGroup
  - EventCaching
  - PropertyTypeFlag
  - LobbyType
  - AuthModeOption
  - CustomAuthenticationType
  - PickupCharacterState
  - CharacterState
  - OnSerializeTransform
  - ViewSynchronization
  - OnSerializeRigidBody
  - OwnershipOption
  - JoinType
  - OpJoinRandomRoomParams
  - BoltAOI
  - BoltPOI
  - BoltDebugStart
  - BoltDebugStartSettings
  - BoltExecutionOrderAttribute
  - BoltNetworkUtils
  - BoltRuntimeConfigs
  alternateRPCAnnotations:
  - PunRPC
  - Photon.Pun.PunRPC
  includePublicClasses: 1
  includePublicMethods: 1
  includePublicFields: 1
  includePublicProperties: 1
  includePublicEvents: 1
  includeProtectedClasses: 1
  includeProtectedMethods: 1
  includeProtectedFields: 1
  includeProtectedProperties: 1
  includeProtectedEvents: 1
  enabled: 1
  onlyObfuscatedSpecifiedNamespaces: 0
  skipNamespacesRecursively: 1
  obfuscateSpecifiedNamespacesRecursively: 1
  obfuscateNamespaces: []
  replaceLiteralsOnSkippedClasses: 1
  obfuscateUnityReflectionMethods: 0
  inheritBeebyteAttributes: 0
  treatScriptableObjectsAsSerializable: 1
  deriveNamesFromCryptoHash: 1
  sha1seed: Wcb8Hbt1eRtPCCdCJFqWz0ca
  numberOfDifferentCharactersAsPowerOfTwo: 4
  numberOfHashBitsToUse: 6
  allowCopyingOfAllAttributesInFakeCode: 0
  allowCopyingOfBeebyteAttributesInFakeCode: 1
  simplifiedNameTranslationForCryptoHash: 0
  reverseNameTranslationOrderPerLine: 1
  namePaddingDelimiter: 0
  preservePrefixes:
  - OnMessage_
  - OnValue_
  - OnAttempt_
  - CanStart_
  - CanStop_
  - OnStart_
  - OnStop_
  - OnFailStart_
  - OnFailStop_
  skipEnums: 1
  maxInstructionsForCloning: 2000
  preserveNamespaces: 0
  obfuscateMonoBehaviourClassNames: 0
  replaceLiterals:
  - OnHover
  - OnSelect
  - OnInput
  - OnScroll
  - OnKey
  - OnPress
  - OnDrag
  - OnClick
  - OnDoubleClick
  - OnDrop
  - OnTooltip
  presetPathClasses: []
  presetPaths: []
  includeParametersOnPublicMethods: 1
  includeParametersOnProtectedMethods: 1
  abstractMonoBehaviours:
  - Photon.MonoBehaviour
  - Photon.PunBehaviour
  - SupportLogging
  useSimplifiedObfuscateLiterals: 0
  translateFakeMethods: 0
  attributesContainingLiteralsToBeReplaced: []
  obfuscateBeebyteTypes: 0
  exposeTranslations: 0
  equivalentAttributesForDoNotFake: []
  equivalentAttributesForObfuscateLiterals: []
  equivalentAttributesForReplaceLiteralsWithName: []
  equivalentAttributesForSkip:
  - Unity.Burst.BurstCompileAttribute
  equivalentAttributesForSkipRename:
  - Newtonsoft.Json.JsonPropertyAttribute
  includeCompilationPipelineAssemblies: 0
  assemblies:
  - Assembly-CSharp.dll
  compiledAssemblies: []
  extraAssemblyDirectories: []
  referencedAssemblies: []
  onlyObfuscateLiteralsInObfuscatedMethods: 0
  obfuscateLiteralsInAllMethods: 0
  noPublicFakeMethods: 0
  progressBar: 1
  obfuscateReleaseOnly: 0
  attributesToRemoveIfObfuscatedMember:
  - UnityEngine.AddComponentMenu
  - UnityEngine.ContextMenu
  - UnityEngine.ContextMenuItem
  - UnityEngine.CreateAssetMenu
  - UnityEngine.Header
  - UnityEngine.HelpURL
  - UnityEngine.Tooltip
  appendGenericTick: 0
  equivalentAttributesForRename: []
  doNotConvertConstantLiteralsToStatics: 0
  useRandomisedNameGenerator: 0
  forcedRandomSeed: 0
  allowShortShaSalt: 0
  printShaSaltInNameTranslationFile: 0
  randomiseShaSaltOnBuild: 1
  equivalentAttributesForSuppressLog: []
  obfuscateAnonymousTypes: 0
  obfuscateAnonymousYieldingEnumeratorTypes: 1
  allowCompilerGeneratedFieldsOnSerializedTypesToBeObfuscated: 0
  literalObfuscationBlockLength: 64
