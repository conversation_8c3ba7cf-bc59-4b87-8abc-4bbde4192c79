// GENERATED BY UNITY. REMOVE THIS COMMENT TO PREVENT OVERWRITING WHEN EXPORTING AGAIN

apply plugin: 'com.android.library'
//apply plugin: 'com.huawei.agconnect'
**APPLY_PLUGINS**



dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.android.support:appcompat-v7:27.1.1'
    implementation 'com.android.support:support-v4:27.1.1'

	implementation 'com.huawei.hms:base:3.0.2.300'
    //implementation 'com.huawei.hms:ppskit:3.0.2.300'
	implementation 'com.huawei.hms:hwid:6.1.0.300'
    implementation 'com.huawei.hms:iap:5.1.0.300'
    implementation 'com.huawei.hms:game:6.1.0.301'

	//classpath 'com.huawei.agconnect:agcp:1.4.1.300'

**DEPS**}

android {
    ndkPath "**NDKPATH**"
    namespace "com.unity3d.player"

    compileSdkVersion **APIVERSION**
    buildToolsVersion '**BUILDTOOLS**'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    defaultConfig {
        minSdkVersion **MINSDKVERSION**
        targetSdkVersion **TARGETSDKVERSION**
        ndk {
            abiFilters **ABIFILTERS**
        }
        versionCode **VERSIONCODE**
        versionName '**VERSIONNAME**'
        consumerProguardFiles 'proguard-unity.txt'**USER_PROGUARD**
	**DEFAULT_CONFIG_SETUP**
    }

    lintOptions {
        abortOnError false
    }

    aaptOptions {
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"
    }

    packagingOptions {
        doNotCompress **BUILTIN_NOCOMPRESS** + unityStreamingAssets.tokenize(', ')
    }**PACKAGING_OPTIONS**
}
**IL_CPP_BUILD_SETUP**
**SOURCE_BUILD_SETUP**
**EXTERNAL_SOURCES**
