Using pre-set license
Built from '2021.3/china_unity/release' branch; Version is '2021.3.21f1c1 (08fa194de70f) revision 588313'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 32717 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files (x86)\2021.3.21f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
G:/GAME_ZD/Brotato_LegionSurvivors
-logFile
Logs/AssetImportWorker2.log
-srvPort
5453
Successfully changed project path to: G:/GAME_ZD/Brotato_LegionSurvivors
G:/GAME_ZD/Brotato_LegionSurvivors
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [15528] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1193659821 [EditorId] 1193659821 [Version] 1048832 [Id] WindowsEditor(7,MS-TFNUFGBLYRYF) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [15528] Host "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1193659821 [EditorId] 1193659821 [Version] 1048832 [Id] WindowsEditor(7,MS-TFNUFGBLYRYF) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.
Refreshing native plugins compatible for Editor in 216.12 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2021.3.21f1c1 (08fa194de70f)
[Subsystems] Discovering subsystems at path D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path G:/GAME_ZD/Brotato_LegionSurvivors/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:   NVIDIA
    VRAM:     5966 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56252
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003938 seconds.
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 295 ms
Refreshing native plugins compatible for Editor in 178.69 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.091 seconds
Domain Reload Profiling:
	ReloadAssembly (1092ms)
		BeginReloadAssembly (98ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (890ms)
			LoadAssemblies (94ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (121ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (29ms)
			SetupLoadedEditorAssemblies (682ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (363ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (179ms)
				BeforeProcessingInitializeOnLoad (1ms)
				ProcessInitializeOnLoadAttributes (94ms)
				ProcessInitializeOnLoadMethodAttributes (45ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.012921 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 188.35 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.399 seconds
Domain Reload Profiling:
	ReloadAssembly (1400ms)
		BeginReloadAssembly (127ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (23ms)
		EndReloadAssembly (1147ms)
			LoadAssemblies (99ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (227ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (76ms)
			SetupLoadedEditorAssemblies (697ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (188ms)
				BeforeProcessingInitializeOnLoad (74ms)
				ProcessInitializeOnLoadAttributes (365ms)
				ProcessInitializeOnLoadMethodAttributes (44ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (6ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 3.09 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3272 Unused Serialized files (Serialized files now loaded: 0)
Unloading 130 unused Assets / (134.3 KB). Loaded Objects now: 3664.
Memory consumption went from 152.4 MB to 152.2 MB.
Total: 5.318600 ms (FindLiveObjects: 0.714300 ms CreateObjectMapping: 0.398600 ms MarkObjects: 4.005600 ms  DeleteObjects: 0.196800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 120255.302250 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map04/Column - Obstruction.png
  artifactKey: Guid(1684cf582eef5554cbf058f65ee3b4e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map04/Column - Obstruction.png using Guid(1684cf582eef5554cbf058f65ee3b4e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '89813adc679b5fbcb58e70e268452da7') in 0.124862 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011426 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.35 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.161 seconds
Domain Reload Profiling:
	ReloadAssembly (1161ms)
		BeginReloadAssembly (140ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (31ms)
		EndReloadAssembly (906ms)
			LoadAssemblies (94ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (206ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (397ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (78ms)
				ProcessInitializeOnLoadAttributes (245ms)
				ProcessInitializeOnLoadMethodAttributes (46ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (78ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.59 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3215 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (107.8 KB). Loaded Objects now: 3674.
Memory consumption went from 151.3 MB to 151.2 MB.
Total: 4.471200 ms (FindLiveObjects: 0.343600 ms CreateObjectMapping: 0.078600 ms MarkObjects: 3.972400 ms  DeleteObjects: 0.075200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 353.346997 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab
  artifactKey: Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab using Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '43bc7c4242c98d53a5601ac4a12e015a') in 0.176939 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014945 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.45 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.094 seconds
Domain Reload Profiling:
	ReloadAssembly (1094ms)
		BeginReloadAssembly (129ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (869ms)
			LoadAssemblies (84ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (195ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (69ms)
			SetupLoadedEditorAssemblies (383ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (239ms)
				ProcessInitializeOnLoadMethodAttributes (45ms)
				AfterProcessingInitializeOnLoad (9ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (81ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.37 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3215 Unused Serialized files (Serialized files now loaded: 0)
Unloading 109 unused Assets / (135.7 KB). Loaded Objects now: 3683.
Memory consumption went from 151.5 MB to 151.4 MB.
Total: 5.942900 ms (FindLiveObjects: 0.410300 ms CreateObjectMapping: 0.101700 ms MarkObjects: 5.096700 ms  DeleteObjects: 0.332400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1788.063636 seconds.
  path: Assets/Monobehavior/CharacterCard/Masochist/Character_Masochist.asset
  artifactKey: Guid(77457821a08b81d4480112e5b5a5031f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Monobehavior/CharacterCard/Masochist/Character_Masochist.asset using Guid(77457821a08b81d4480112e5b5a5031f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (Weapon: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

SendMessage cannot be called during Awake, CheckConsistency, or OnValidate (HidghLight: OnSpriteTilingPropertyChange)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditor.AssetDatabase:LoadAssetAtPath<GameData> (string)
SignletonsDataBase`1<GameData>:get_Instance () (at Assets/Scripts/Data/Singletons/SignletonsDataBase.cs:24)
Effect:OnValidate () (at Assets/Scripts/Data/Effect/Effect.cs:121)

[Assets/Scripts/Data/Singletons/SignletonsDataBase.cs line 24]

 -> (artifact id: '9784af82d093202441dd20bd05dfc52f') in 1.127156 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011126 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.35 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.206 seconds
Domain Reload Profiling:
	ReloadAssembly (1207ms)
		BeginReloadAssembly (141ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (33ms)
		EndReloadAssembly (955ms)
			LoadAssemblies (101ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (217ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (416ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (83ms)
				ProcessInitializeOnLoadAttributes (256ms)
				ProcessInitializeOnLoadMethodAttributes (48ms)
				AfterProcessingInitializeOnLoad (10ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (83ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 16.54 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3215 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (107.8 KB). Loaded Objects now: 3686.
Memory consumption went from 153.3 MB to 153.2 MB.
Total: 4.789900 ms (FindLiveObjects: 0.435600 ms CreateObjectMapping: 0.178200 ms MarkObjects: 4.061700 ms  DeleteObjects: 0.112700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 70.877715 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab
  artifactKey: Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab using Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bf39ca7bdb856bc6b3fc1494e0a079e2') in 0.209615 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013919 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.05 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.166 seconds
Domain Reload Profiling:
	ReloadAssembly (1167ms)
		BeginReloadAssembly (149ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (917ms)
			LoadAssemblies (98ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (213ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (77ms)
			SetupLoadedEditorAssemblies (397ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (19ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (241ms)
				ProcessInitializeOnLoadMethodAttributes (45ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (80ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 17.98 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3215 Unused Serialized files (Serialized files now loaded: 0)
Unloading 109 unused Assets / (135.6 KB). Loaded Objects now: 3689.
Memory consumption went from 153.4 MB to 153.3 MB.
Total: 5.258400 ms (FindLiveObjects: 0.445400 ms CreateObjectMapping: 0.113900 ms MarkObjects: 4.474900 ms  DeleteObjects: 0.222400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 158.796785 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab
  artifactKey: Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab using Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '014bdb6ea63282e3b404eed85f3ffd26') in 0.158653 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012767 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.26 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.138 seconds
Domain Reload Profiling:
	ReloadAssembly (1138ms)
		BeginReloadAssembly (138ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (895ms)
			LoadAssemblies (89ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (202ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (70ms)
			SetupLoadedEditorAssemblies (391ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (16ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (76ms)
				ProcessInitializeOnLoadAttributes (241ms)
				ProcessInitializeOnLoadMethodAttributes (47ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (82ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 16.35 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3215 Unused Serialized files (Serialized files now loaded: 0)
Unloading 109 unused Assets / (135.7 KB). Loaded Objects now: 3692.
Memory consumption went from 153.4 MB to 153.3 MB.
Total: 4.589000 ms (FindLiveObjects: 0.391800 ms CreateObjectMapping: 0.098100 ms MarkObjects: 3.813900 ms  DeleteObjects: 0.283800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 254.976320 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab
  artifactKey: Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab using Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e4d314771f8a8f3ad5d27ac6d47b8387') in 0.179018 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013515 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.83 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.197 seconds
Domain Reload Profiling:
	ReloadAssembly (1197ms)
		BeginReloadAssembly (143ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (949ms)
			LoadAssemblies (91ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (218ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (87ms)
			SetupLoadedEditorAssemblies (408ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (83ms)
				ProcessInitializeOnLoadAttributes (252ms)
				ProcessInitializeOnLoadMethodAttributes (45ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (72ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 19.90 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3215 Unused Serialized files (Serialized files now loaded: 0)
Unloading 109 unused Assets / (135.6 KB). Loaded Objects now: 3695.
Memory consumption went from 153.5 MB to 153.3 MB.
Total: 4.845300 ms (FindLiveObjects: 0.484800 ms CreateObjectMapping: 0.126500 ms MarkObjects: 4.049500 ms  DeleteObjects: 0.183100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 151.206287 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab
  artifactKey: Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab using Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '68815cd9009c58a91d9d66ad5422286e') in 0.160266 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.010618 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.24 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.166 seconds
Domain Reload Profiling:
	ReloadAssembly (1167ms)
		BeginReloadAssembly (137ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (920ms)
			LoadAssemblies (87ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (220ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (393ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (80ms)
				ProcessInitializeOnLoadAttributes (243ms)
				ProcessInitializeOnLoadMethodAttributes (43ms)
				AfterProcessingInitializeOnLoad (7ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (79ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.98 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3215 Unused Serialized files (Serialized files now loaded: 0)
Unloading 109 unused Assets / (135.7 KB). Loaded Objects now: 3698.
Memory consumption went from 153.5 MB to 153.4 MB.
Total: 4.729200 ms (FindLiveObjects: 0.357700 ms CreateObjectMapping: 0.097700 ms MarkObjects: 4.042100 ms  DeleteObjects: 0.229800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 131.197670 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab
  artifactKey: Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab using Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '63ba02b1b590bede4d14247ddee6acdd') in 0.165317 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012317 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 2.51 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.163 seconds
Domain Reload Profiling:
	ReloadAssembly (1164ms)
		BeginReloadAssembly (142ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (33ms)
		EndReloadAssembly (909ms)
			LoadAssemblies (93ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (213ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (79ms)
			SetupLoadedEditorAssemblies (397ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (17ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (245ms)
				ProcessInitializeOnLoadMethodAttributes (44ms)
				AfterProcessingInitializeOnLoad (7ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (73ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.95 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3215 Unused Serialized files (Serialized files now loaded: 0)
Unloading 109 unused Assets / (135.7 KB). Loaded Objects now: 3701.
Memory consumption went from 153.6 MB to 153.4 MB.
Total: 5.336500 ms (FindLiveObjects: 0.493500 ms CreateObjectMapping: 0.119200 ms MarkObjects: 4.537700 ms  DeleteObjects: 0.183900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 259.086941 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map04/Door11.png
  artifactKey: Guid(776fde1b362a2a04494095cb618dd5d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map04/Door11.png using Guid(776fde1b362a2a04494095cb618dd5d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '36f6f7ac997c8024788dd08da61603dd') in 0.085208 seconds 
========================================================================
Received Import Request.
  Time since last request: 75.673661 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map04/Door11.png
  artifactKey: Guid(776fde1b362a2a04494095cb618dd5d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map04/Door11.png using Guid(776fde1b362a2a04494095cb618dd5d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '11c2da311a469885030c69a3b9a6d965') in 0.066348 seconds 
========================================================================
Received Import Request.
  Time since last request: 96.386512 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map04/Door11.png
  artifactKey: Guid(776fde1b362a2a04494095cb618dd5d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map04/Door11.png using Guid(776fde1b362a2a04494095cb618dd5d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '13566631e3bf1de91211ec9632404721') in 0.043556 seconds 
========================================================================
Received Import Request.
  Time since last request: 145.030657 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map04/Door11 1.png
  artifactKey: Guid(d792906fab3cc7a47860a20282086684) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map04/Door11 1.png using Guid(d792906fab3cc7a47860a20282086684) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b4f5a2ab3c40d375cd74d72dc7f85fe8') in 0.032828 seconds 
========================================================================
Received Import Request.
  Time since last request: 4.555072 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map04/Door22.png
  artifactKey: Guid(d792906fab3cc7a47860a20282086684) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map04/Door22.png using Guid(d792906fab3cc7a47860a20282086684) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1870b00e0f99b35ba26b712c492c06ea') in 0.030241 seconds 
========================================================================
Received Import Request.
  Time since last request: 23.378963 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map04/Door22.png
  artifactKey: Guid(d792906fab3cc7a47860a20282086684) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map04/Door22.png using Guid(d792906fab3cc7a47860a20282086684) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6bea5f2f5c4fe41c09c60da8dd4ef54a') in 0.043932 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015017 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Refreshing native plugins compatible for Editor in 3.07 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.199 seconds
Domain Reload Profiling:
	ReloadAssembly (1200ms)
		BeginReloadAssembly (137ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (954ms)
			LoadAssemblies (94ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (219ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (86ms)
			SetupLoadedEditorAssemblies (412ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (18ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (3ms)
				BeforeProcessingInitializeOnLoad (85ms)
				ProcessInitializeOnLoadAttributes (256ms)
				ProcessInitializeOnLoadMethodAttributes (42ms)
				AfterProcessingInitializeOnLoad (8ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (76ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 18.34 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3215 Unused Serialized files (Serialized files now loaded: 0)
Unloading 105 unused Assets / (107.7 KB). Loaded Objects now: 3707.
Memory consumption went from 153.5 MB to 153.4 MB.
Total: 4.920900 ms (FindLiveObjects: 0.375400 ms CreateObjectMapping: 0.080200 ms MarkObjects: 4.372100 ms  DeleteObjects: 0.091400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 546.936762 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab
  artifactKey: Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/ChildMapNew.prefab using Guid(23c83cfd4dff4d847944dbcbd8a47b5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '57e22af5d5b273a65a10610bcd25b1eb') in 0.184023 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map1.png
  artifactKey: Guid(baf52c9583007ce41b992b8608d7ecb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map1.png using Guid(baf52c9583007ce41b992b8608d7ecb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0ace865ab0500a0871f35c2c14566084') in 0.400529 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map4.psd
  artifactKey: Guid(9c83d743e8c4bfb4e999b7c23a7549c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map4.psd using Guid(9c83d743e8c4bfb4e999b7c23a7549c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bd3415b56de2b1d7995121fd19c4b77c') in 0.332570 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map_04_backUp.prefab
  artifactKey: Guid(39c28562f3808f840b1c69cd243f799e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map_04_backUp.prefab using Guid(39c28562f3808f840b1c69cd243f799e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cd76694ba7b23b3f88e8c2dfade4fbf9') in 0.091932 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map_02.prefab
  artifactKey: Guid(de903f43b3ea7bf42848a526d554d171) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map_02.prefab using Guid(de903f43b3ea7bf42848a526d554d171) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ed30b8175707a5f0236a5aad09983d01') in 0.110175 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map5.png
  artifactKey: Guid(16f86cfee74f7af47b869832295cbbae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map5.png using Guid(16f86cfee74f7af47b869832295cbbae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '966700a247b51bb50e258610e47bcc5b') in 0.355878 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map_01.prefab
  artifactKey: Guid(4d343f0dc3a12234aa08373b6b252c34) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map_01.prefab using Guid(4d343f0dc3a12234aa08373b6b252c34) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '346a79b60cc138e14ce91cefe540f2a7') in 0.105418 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map_03.prefab
  artifactKey: Guid(f13b097cf2046d54db2fe9384417ec35) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map_03.prefab using Guid(f13b097cf2046d54db2fe9384417ec35) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1f3c70e57053291e143236ca8cde6360') in 0.078281 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map_04.prefab
  artifactKey: Guid(c1fe748aa87d5124dba462bf8cbffe85) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map_04.prefab using Guid(c1fe748aa87d5124dba462bf8cbffe85) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '31d064a39a18efa00741277f504fc027') in 0.087283 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/ScenesPortrait_dungeon/Map/Map_Base.prefab
  artifactKey: Guid(00486e5e119ca0f46bd6415cc20b1dd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ScenesPortrait_dungeon/Map/Map_Base.prefab using Guid(00486e5e119ca0f46bd6415cc20b1dd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c5226c7c7d28404297c1294d353de243') in 0.075170 seconds 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0