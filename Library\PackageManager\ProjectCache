m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: G:/GAME_ZD/Brotato_LegionSurvivors/Packages/manifest.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638841122859914922
    m_Hash: 1449567951
  m_LockFileStatus:
    m_FilePath: G:/GAME_ZD/Brotato_LegionSurvivors/Packages/packages-lock.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638841122866367654
    m_Hash: 2537870293
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_LocalPackages:
  m_LocalFileStatus: []
m_ProjectPath: G:/GAME_ZD/Brotato_LegionSurvivors/Packages
m_EditorVersion: 2021.3.21f1c1 (08fa194de70f)
m_ResolvedPackages:
- packageId: com.unity.2d.pixel-perfect@5.0.3
  testable: 0
  isDirectDependency: 1
  version: 5.0.3
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.2d.pixel-perfect@5.0.3
  assetPath: Packages/com.unity.2d.pixel-perfect
  name: com.unity.2d.pixel-perfect
  displayName: 2D Pixel Perfect
  author:
    name: 
    email: 
    url: 
  category: 
  type: tool
  description: 'The 2D Pixel Perfect package contains the Pixel Perfect Camera component
    which ensures your pixel art remains crisp and clear at different resolutions,
    and stable in motion.


    It is a single component that makes all the calculations
    needed to scale the viewport with resolution changes, removing the hassle from
    the user. The user can adjust the definition of the pixel art rendered within
    the camera viewport through the component settings, as well preview any changes
    immediately in Game view by using the Run in Edit Mode feature.'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0-preview
    - 1.0.1-preview
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.1.0
    - 3.0.0
    - 3.0.1
    - 3.0.2
    - 4.0.0
    - 4.0.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.0.3
    - 5.1.0
    compatible:
    - 5.0.3
    - 5.1.0
    verified: 5.0.3
  dependencies: []
  resolvedDependencies: []
  keywords:
  - pixel
  - perfect
  - 2D
  - sprite
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638023910590000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: 6dea8ac5bd6953acd64f1ac9d470912954b0c015
    path: 
  unityLifecycle:
    version: 5.0.3
    nextVersion: 
- packageId: com.unity.2d.psdimporter@6.0.7
  testable: 0
  isDirectDependency: 1
  version: 6.0.7
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.2d.psdimporter@6.0.7
  assetPath: Packages/com.unity.2d.psdimporter
  name: com.unity.2d.psdimporter
  displayName: 2D PSD Importer
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: A ScriptedImporter for importing Adobe Photoshop PSB (Photoshop Big)
    file format. The ScriptedImporter is currently targeted for users who wants to
    create multi Sprite character animation using Unity 2D Animation Package.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0-preview.1
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.1.0-preview.1
    - 1.1.0-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.3
    - 1.2.0-preview.4
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.7
    - 2.1.0
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.1.6
    - 2.1.8
    - 2.1.9
    - 2.1.10
    - 2.1.11
    - 3.0.0
    - 3.1.1
    - 3.1.3
    - 3.1.4
    - 3.1.5
    - 3.1.6
    - 3.1.7
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.1.3
    - 4.2.0
    - 4.3.0
    - 4.3.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.1
    - 5.0.3
    - 5.0.4
    - 5.0.6
    - 6.0.0-pre.2
    - 6.0.0-pre.3
    - 6.0.0-pre.4
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.6
    - 6.0.7
    - 6.0.8
    - 6.0.9
    - 6.1.0
    - 7.0.0-pre.3
    - 7.0.0-pre.4
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 8.0.0-pre.1
    - 8.0.0-pre.3
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.0.5
    - 8.1.0
    - 9.0.0-pre.1
    - 9.0.0-pre.2
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 10.0.0
    - 10.1.0
    - 10.1.1
    compatible:
    - 6.0.7
    - 6.0.8
    - 6.0.9
    - 6.1.0
    verified: 6.0.9
  dependencies:
  - name: com.unity.2d.animation
    version: 7.0.9
  - name: com.unity.2d.common
    version: 6.0.6
  - name: com.unity.2d.sprite
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.animation
    version: 7.0.9
  - name: com.unity.2d.common
    version: 6.0.6
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.2.6
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.uielementsnative
    version: 1.0.0
  - name: com.unity.burst
    version: 1.6.6
  - name: com.unity.modules.animation
    version: 1.0.0
  keywords:
  - 2d
  - psdimporter
  - assetimporter
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638067887380000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.psdimporter@6.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: 911204bb63dcf2da80b6063d54c4ec8737ad13dc
    path: 
  unityLifecycle:
    version: 6.0.7
    nextVersion: 
- packageId: com.unity.2d.spriteshape@7.0.6
  testable: 0
  isDirectDependency: 1
  version: 7.0.6
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.2d.spriteshape@7.0.6
  assetPath: Packages/com.unity.2d.spriteshape
  name: com.unity.2d.spriteshape
  displayName: 2D SpriteShape
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: SpriteShape Runtime & Editor Package contains the tooling and the
    runtime component that allows you to create very organic looking spline based
    2D worlds. It comes with intuitive configurator and a highly performant renderer.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.10-preview.2
    - 1.0.11-preview
    - 1.0.12-preview
    - 1.0.12-preview.1
    - 1.0.14-preview.2
    - 2.0.0-preview.4
    - 2.0.0-preview.5
    - 2.0.0-preview.7
    - 2.0.0-preview.8
    - 2.0.0-preview.9
    - 2.1.0-preview.2
    - 2.1.0-preview.6
    - 2.1.0-preview.7
    - 2.1.0-preview.10
    - 2.1.0-preview.11
    - 3.0.1
    - 3.0.2
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.8
    - 3.0.9
    - 3.0.10
    - 3.0.11
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.0.3
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.1.3
    - 4.1.4
    - 4.1.5
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.1.0
    - 5.1.1
    - 5.1.2
    - 5.1.3
    - 5.1.4
    - 5.1.5
    - 5.1.6
    - 5.1.7
    - 5.2.0
    - 5.3.0
    - 6.0.0-pre.1
    - 6.0.0-pre.2
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 7.0.0-pre.2
    - 7.0.0-pre.3
    - 7.0.0
    - 7.0.2
    - 7.0.3
    - 7.0.4
    - 7.0.5
    - 7.0.6
    - 7.0.7
    - 7.1.0
    - 8.0.0-pre.4
    - 8.0.0-pre.5
    - 8.0.0
    - 8.0.1
    - 9.0.0-pre.1
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.0.5
    - 9.1.0
    - 10.0.0-pre.1
    - 10.0.0-pre.2
    - 10.0.0
    - 10.0.1
    - 10.0.2
    - 10.0.3
    - 10.0.4
    - 10.0.5
    - 10.0.6
    - 10.0.7
    - 10.1.0
    - 11.0.0
    compatible:
    - 7.0.6
    - 7.0.7
    - 7.1.0
    verified: 7.0.7
  dependencies:
  - name: com.unity.mathematics
    version: 1.1.0
  - name: com.unity.2d.common
    version: 6.0.4
  - name: com.unity.2d.path
    version: 5.0.2
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.2.6
  - name: com.unity.2d.common
    version: 6.0.6
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.uielementsnative
    version: 1.0.0
  - name: com.unity.burst
    version: 1.6.6
  - name: com.unity.2d.path
    version: 5.0.2
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords:
  - 2d
  - shape
  - spriteshape
  - smartsprite
  - spline
  - terrain2d
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637958913420000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: 6b783c5dfb5a72d73e2b0a70d1caf4a367c58805
    path: 
  unityLifecycle:
    version: 7.0.6
    nextVersion: 
- packageId: com.unity.2d.tilemap@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.2d.tilemap@1.0.0
  assetPath: Packages/com.unity.2d.tilemap
  name: com.unity.2d.tilemap
  displayName: 2D Tilemap Editor
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Tilemap Editor is a package that contains editor functionalities
    for editing Tilemaps.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords:
  - 2d
  - Tilemap
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.addressables@1.19.19
  testable: 0
  isDirectDependency: 1
  version: 1.19.19
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.addressables@1.19.19
  assetPath: Packages/com.unity.addressables
  name: com.unity.addressables
  displayName: Addressables
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'The Addressable Asset System allows the developer to ask for an asset
    via its address. Once an asset (e.g. a prefab) is marked "addressable", it generates
    an address which can be called from anywhere. Wherever the asset resides (local
    or remote), the system will locate it and its dependencies, then return it.


    Use
    ''Window->Asset Management->Addressables'' to begin working with the system.


    Addressables
    use asynchronous loading to support loading from any location with any collection
    of dependencies. Whether you have been using direct references, traditional asset
    bundles, or Resource folders, addressables provide a simpler way to make your
    game more dynamic. Addressables simultaneously opens up the world of asset bundles
    while managing all the complexity.


    For usage samples, see github.com/Unity-Technologies/Addressables-Sample'
  status: 0
  errors: []
  versions:
    all:
    - 0.0.8-preview
    - 0.0.12-preview
    - 0.0.15-preview
    - 0.0.16-preview
    - 0.0.18-preview
    - 0.0.22-preview
    - 0.0.26-preview
    - 0.0.27-preview
    - 0.1.2-preview
    - 0.2.1-preview
    - 0.2.2-preview
    - 0.3.5-preview
    - 0.4.6-preview
    - 0.4.8-preview
    - 0.5.2-preview
    - 0.5.3-preview
    - 0.6.6-preview
    - 0.6.7-preview
    - 0.6.8-preview
    - 0.7.4-preview
    - 0.7.5-preview
    - 0.8.4-preview
    - 0.8.6-preview
    - 1.1.3-preview
    - 1.1.4-preview
    - 1.1.5
    - 1.1.7
    - 1.1.9
    - 1.1.10
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.3.3
    - 1.3.8
    - 1.4.0
    - 1.5.0
    - 1.5.1
    - 1.6.0
    - 1.6.2
    - 1.7.4
    - 1.7.5
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.9.2
    - 1.10.0
    - 1.11.2
    - 1.12.0
    - 1.13.1
    - 1.14.2
    - 1.15.1
    - 1.16.1
    - 1.16.6
    - 1.16.7
    - 1.16.8
    - 1.16.10
    - 1.16.12
    - 1.16.13
    - 1.16.15
    - 1.16.16
    - 1.16.19
    - 1.17.0-preview
    - 1.17.2-preview
    - 1.17.4-preview
    - 1.17.5-preview
    - 1.17.6-preview
    - 1.17.13
    - 1.17.15
    - 1.17.17
    - 1.18.2
    - 1.18.4
    - 1.18.9
    - 1.18.11
    - 1.18.13
    - 1.18.15
    - 1.18.16
    - 1.18.19
    - 1.19.4
    - 1.19.6
    - 1.19.9
    - 1.19.11
    - 1.19.13
    - 1.19.14
    - 1.19.15
    - 1.19.17
    - 1.19.18
    - 1.19.19
    - 1.20.0
    - 1.20.3
    - 1.20.5
    - 1.21.1
    - 1.21.2
    - 1.21.3
    - 1.21.8
    - 1.21.9
    - 1.21.10
    - 1.21.12
    - 1.21.14
    - 1.21.15
    - 1.21.17
    - 1.21.18
    - 1.21.19
    - 1.21.20
    - 1.21.21
    - 1.22.2
    - 1.22.3
    - 1.23.1
    - 1.24.0
    - 1.25.0
    - 2.0.3
    - 2.0.4
    - 2.0.6
    - 2.0.8
    - 2.1.0
    - 2.2.2
    - 2.3.0
    - 2.3.1
    - 2.3.7
    - 2.3.16
    - 2.4.1
    - 2.4.2
    - 2.4.3
    - 2.4.4
    - 2.4.5
    - 2.4.6
    - 2.5.0
    compatible:
    - 1.19.19
    - 1.20.0
    - 1.20.3
    - 1.20.5
    - 1.21.1
    - 1.21.2
    - 1.21.3
    - 1.21.8
    - 1.21.9
    - 1.21.10
    - 1.21.12
    - 1.21.14
    - 1.21.15
    - 1.21.17
    - 1.21.18
    - 1.21.19
    - 1.21.20
    - 1.21.21
    - 1.22.2
    - 1.22.3
    - 1.23.1
    - 1.24.0
    - 1.25.0
    verified: 1.19.19
  dependencies:
  - name: com.unity.scriptablebuildpipeline
    version: 1.19.6
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.scriptablebuildpipeline
    version: 1.20.1
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  keywords:
  - asset
  - resources
  - bundle
  - bundles
  - assetbundles
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637819852270000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/Addressables.git
    revision: 2ac2ed2277c173a9a41ffeafa080525578c38e4f
    path: 
  unityLifecycle:
    version: 1.19.19
    nextVersion: 
- packageId: com.unity.cinemachine@2.8.9
  testable: 0
  isDirectDependency: 1
  version: 2.8.9
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.cinemachine@2.8.9
  assetPath: Packages/com.unity.cinemachine
  name: com.unity.cinemachine
  displayName: Cinemachine
  author:
    name: 
    email: 
    url: 
  category: cinematography
  type: 
  description: "Smart camera tools for passionate creators. \n\nNew starting from
    2.7.1: Are you looking for the Cinemachine menu? It has moved to the GameObject
    menu.\n\nIMPORTANT NOTE: If you are upgrading from the legacy Asset Store version
    of Cinemachine, delete the Cinemachine asset from your project BEFORE installing
    this version from the Package Manager."
  status: 0
  errors: []
  versions:
    all:
    - 2.1.11-beta.1
    - 2.1.12
    - 2.1.13
    - 2.2.0
    - 2.2.7
    - 2.2.8
    - 2.2.9
    - 2.2.10-preview.3
    - 2.2.10-preview.4
    - 2.3.1
    - 2.3.3
    - 2.3.4
    - 2.3.5-preview.3
    - 2.4.0-preview.3
    - 2.4.0-preview.4
    - 2.4.0-preview.6
    - 2.4.0-preview.7
    - 2.4.0-preview.8
    - 2.4.0-preview.9
    - 2.4.0-preview.10
    - 2.4.0
    - 2.5.0
    - 2.6.0-preview.2
    - 2.6.0-preview.3
    - 2.6.0-preview.5
    - 2.6.0-preview.8
    - 2.6.0
    - 2.6.1-preview.6
    - 2.6.1
    - 2.6.2-preview.1
    - 2.6.2
    - 2.6.3-preview.2
    - 2.6.3
    - 2.6.4
    - 2.6.5
    - 2.6.9
    - 2.6.10
    - 2.6.11
    - 2.6.14
    - 2.6.15
    - 2.6.17
    - 2.7.1
    - 2.7.2
    - 2.7.3
    - 2.7.4
    - 2.7.5
    - 2.7.8
    - 2.7.9
    - 2.8.0-exp.1
    - 2.8.0-exp.2
    - 2.8.0-pre.1
    - 2.8.0
    - 2.8.1
    - 2.8.2
    - 2.8.3
    - 2.8.4
    - 2.8.6
    - 2.8.9
    - 2.9.0-pre.1
    - 2.9.0-pre.6
    - 2.9.1
    - 2.9.2
    - 2.9.4
    - 2.9.5
    - 2.9.7
    - 2.10.0
    - 2.10.1
    - 2.10.2
    - 2.10.3
    - 3.0.0-pre.3
    - 3.0.0-pre.4
    - 3.0.0-pre.5
    - 3.0.0-pre.6
    - 3.0.0-pre.7
    - 3.0.0-pre.8
    - 3.0.0-pre.9
    - 3.0.1
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    compatible:
    - 2.8.9
    - 2.9.0-pre.1
    - 2.9.0-pre.6
    - 2.9.1
    - 2.9.2
    - 2.9.4
    - 2.9.5
    - 2.9.7
    - 2.10.0
    - 2.10.1
    - 2.10.2
    - 2.10.3
    verified: 2.8.9
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.31
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.1.31
  - name: com.unity.ext.nunit
    version: 1.0.6
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - camera
  - follow
  - rig
  - fps
  - cinematography
  - aim
  - orbit
  - cutscene
  - cinematic
  - collision
  - freelook
  - cinemachine
  - compose
  - composition
  - dolly
  - track
  - clearshot
  - noise
  - framing
  - handheld
  - lens
  - impulse
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637975495930000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.cinemachine.git
    revision: 92bcde243d623d77c64960a552f29396770af1e1
    path: 
  unityLifecycle:
    version: 2.8.9
    nextVersion: 
- packageId: com.unity.ide.rider@3.0.18
  testable: 0
  isDirectDependency: 1
  version: 3.0.18
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.ide.rider@3.0.18
  assetPath: Packages/com.unity.ide.rider
  name: com.unity.ide.rider
  displayName: JetBrains Rider Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The JetBrains Rider Editor package provides an integration for using
    the JetBrains Rider IDE as a code editor for Unity. It adds support for generating
    .csproj files for code completion and auto-discovery of installations.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.6
    - 1.0.8
    - 1.1.0
    - 1.1.1
    - 1.1.2-preview
    - 1.1.2-preview.2
    - 1.1.3-preview.1
    - 1.1.4-preview
    - 1.1.4
    - 1.2.0-preview
    - 1.2.1
    - 2.0.0-preview
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.9
    - 3.0.10
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 3.0.20
    - 3.0.21
    - 3.0.22
    - 3.0.24
    - 3.0.25
    - 3.0.26
    - 3.0.27
    - 3.0.28
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    compatible:
    - 3.0.18
    - 3.0.20
    - 3.0.21
    - 3.0.22
    - 3.0.24
    - 3.0.25
    - 3.0.26
    - 3.0.27
    - 3.0.28
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    verified: 3.0.36
  dependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638088746970000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.rider@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.rider.git
    revision: b40b39e6834c2915637f431d002d1f3dd8ec9ce5
    path: 
  unityLifecycle:
    version: 3.0.18
    nextVersion: 
- packageId: com.unity.ide.visualstudio@2.0.17
  testable: 0
  isDirectDependency: 1
  version: 2.0.17
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.ide.visualstudio@2.0.17
  assetPath: Packages/com.unity.ide.visualstudio
  name: com.unity.ide.visualstudio
  displayName: Visual Studio Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Code editor integration for supporting Visual Studio as code editor
    for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.9
    - 1.0.10
    - 1.0.11
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 2.0.8
    - 2.0.9
    - 2.0.11
    - 2.0.12
    - 2.0.14
    - 2.0.15
    - 2.0.16
    - 2.0.17
    - 2.0.18
    - 2.0.20
    - 2.0.21
    - 2.0.22
    - 2.0.23
    compatible:
    - 2.0.17
    - 2.0.18
    - 2.0.20
    - 2.0.21
    - 2.0.22
    - 2.0.23
    verified: 2.0.23
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.9
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.1.31
  - name: com.unity.ext.nunit
    version: 1.0.6
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638059987360000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
    revision: 1f126893bfb18ea9661fb15771613e841467073c
    path: 
  unityLifecycle:
    version: 2.0.17
    nextVersion: 
- packageId: com.unity.ide.vscode@1.2.5
  testable: 0
  isDirectDependency: 1
  version: 1.2.5
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.ide.vscode@1.2.5
  assetPath: Packages/com.unity.ide.vscode
  name: com.unity.ide.vscode
  displayName: Visual Studio Code Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Code editor integration for supporting Visual Studio Code as code
    editor for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.7
    - 1.1.0
    - 1.1.2
    - 1.1.3
    - 1.1.4
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    compatible:
    - 1.2.5
    verified: 1.2.5
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637800255360000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.ide.vscode.git
    revision: b0740c80bfc2440527c317109f7c3d9100132722
    path: 
  unityLifecycle:
    version: 1.2.5
    nextVersion: 
- packageId: com.unity.test-framework@1.1.31
  testable: 0
  isDirectDependency: 1
  version: 1.1.31
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.test-framework@1.1.31
  assetPath: Packages/com.unity.test-framework
  name: com.unity.test-framework
  displayName: Test Framework
  author:
    name: 
    email: 
    url: 
  category: Unity Test Framework
  type: 
  description: Test framework for running Edit mode and Play mode tests in Unity.
  status: 0
  errors: []
  versions:
    all:
    - 0.0.4-preview
    - 0.0.29-preview
    - 1.0.0
    - 1.0.7
    - 1.0.9
    - 1.0.11
    - 1.0.12
    - 1.0.13
    - 1.0.14
    - 1.0.16
    - 1.0.17
    - 1.0.18
    - 1.1.0
    - 1.1.1
    - 1.1.2
    - 1.1.3
    - 1.1.5
    - 1.1.8
    - 1.1.9
    - 1.1.11
    - 1.1.13
    - 1.1.14
    - 1.1.16
    - 1.1.18
    - 1.1.19
    - 1.1.20
    - 1.1.22
    - 1.1.24
    - 1.1.26
    - 1.1.27
    - 1.1.29
    - 1.1.30
    - 1.1.31
    - 1.1.33
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    compatible:
    - 1.1.31
    - 1.1.33
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    verified: 1.1.33
  dependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - Test
  - TestFramework
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637804266210000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.test-framework.git
    revision: 438b0defc147c9be5c969ca79ff03a722b4590ed
    path: 
  unityLifecycle:
    version: 1.1.31
    nextVersion: 
- packageId: com.unity.textmeshpro@3.0.6
  testable: 0
  isDirectDependency: 1
  version: 3.0.6
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.textmeshpro@3.0.6
  assetPath: Packages/com.unity.textmeshpro
  name: com.unity.textmeshpro
  displayName: TextMeshPro
  author:
    name: 
    email: 
    url: 
  category: Text Rendering
  type: 
  description: 'TextMeshPro is the ultimate text solution for Unity. It''s the perfect
    replacement for Unity''s UI Text and the legacy Text Mesh.


    Powerful and
    easy to use, TextMeshPro (also known as TMP) uses Advanced Text Rendering techniques
    along with a set of custom shaders; delivering substantial visual quality improvements
    while giving users incredible flexibility when it comes to text styling and texturing.


    TextMeshPro
    provides Improved Control over text formatting and layout with features like
    character, word, line and paragraph spacing, kerning, justified text, Links,
    over 30 Rich Text Tags available, support for Multi Font & Sprites, Custom Styles
    and more.


    Great performance. Since the geometry created by TextMeshPro
    uses two triangles per character just like Unity''s text components, this improved
    visual quality and flexibility comes at no additional performance cost.'
  status: 0
  errors: []
  versions:
    all:
    - 0.1.2
    - 1.0.21
    - 1.0.23
    - 1.0.25
    - 1.0.26
    - 1.1.0
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.3.0-preview
    - 1.3.0
    - 1.4.0-preview.1b
    - 1.4.0-preview.2a
    - 1.4.0-preview.3a
    - 1.4.0
    - 1.4.1-preview.1
    - 1.4.1
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.0-preview.6
    - 1.5.0-preview.7
    - 1.5.0-preview.8
    - 1.5.0-preview.10
    - 1.5.0-preview.11
    - 1.5.0-preview.12
    - 1.5.0-preview.13
    - 1.5.0-preview.14
    - 1.5.0
    - 1.5.1
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.6.0-preview.1
    - 2.0.0
    - 2.0.1-preview.1
    - 2.0.1
    - 2.1.0-preview.1
    - 2.1.0-preview.2
    - 2.1.0-preview.3
    - 2.1.0-preview.4
    - 2.1.0-preview.5
    - 2.1.0-preview.7
    - 2.1.0-preview.8
    - 2.1.0-preview.10
    - 2.1.0-preview.11
    - 2.1.0-preview.12
    - 2.1.0-preview.13
    - 2.1.0-preview.14
    - 2.1.0
    - 2.1.1
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.1.6
    - 2.2.0-preview.1
    - 2.2.0-preview.2
    - 2.2.0-preview.3
    - 3.0.0-preview.1
    - 3.0.0-preview.3
    - 3.0.0-preview.4
    - 3.0.0-preview.5
    - 3.0.0-preview.7
    - 3.0.0-preview.8
    - 3.0.0-preview.10
    - 3.0.0-preview.11
    - 3.0.0-preview.12
    - 3.0.0-preview.13
    - 3.0.0-preview.14
    - 3.0.0
    - 3.0.1
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.8
    - 3.0.9
    - 3.2.0-pre.1
    - 3.2.0-pre.2
    - 3.2.0-pre.3
    - 3.2.0-pre.4
    - 3.2.0-pre.5
    - 3.2.0-pre.6
    - 3.2.0-pre.7
    - 3.2.0-pre.8
    - 3.2.0-pre.9
    - 3.2.0-pre.10
    - 3.2.0-pre.11
    - 3.2.0-pre.12
    - 4.0.0-pre.1
    - 4.0.0-pre.2
    compatible:
    - 3.0.6
    - 3.0.7
    - 3.0.8
    - 3.0.9
    - 3.2.0-pre.1
    - 3.2.0-pre.2
    - 3.2.0-pre.3
    - 3.2.0-pre.4
    - 3.2.0-pre.5
    - 3.2.0-pre.6
    - 3.2.0-pre.7
    - 3.2.0-pre.8
    - 3.2.0-pre.9
    - 3.2.0-pre.10
    - 3.2.0-pre.11
    - 3.2.0-pre.12
    verified: 3.0.9
  dependencies:
  - name: com.unity.ugui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - TextMeshPro
  - TextMesh Pro
  - TMP
  - Text
  - SDF
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637547111200000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.textmeshpro.git
    revision: 01e57e3b7de18af9dfe9baaa0a0ff5cc4765c899
    path: 
  unityLifecycle:
    version: 3.0.6
    nextVersion: 
- packageId: com.unity.timeline@1.6.4
  testable: 0
  isDirectDependency: 1
  version: 1.6.4
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.timeline@1.6.4
  assetPath: Packages/com.unity.timeline
  name: com.unity.timeline
  displayName: Timeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Use Unity Timeline to create cinematic content, game-play sequences,
    audio sequences, and complex particle effects.
  status: 0
  errors: []
  versions:
    all:
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.10
    - 1.2.11
    - 1.2.12
    - 1.2.13
    - 1.2.14
    - 1.2.15
    - 1.2.16
    - 1.2.17
    - 1.2.18
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.5
    - 1.4.0-preview.6
    - 1.4.0-preview.7
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.5.0-pre.2
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.1-pre.1
    - 1.5.1-pre.2
    - 1.5.1-pre.3
    - 1.5.2
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.5.7
    - 1.6.0-pre.1
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0-pre.5
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.7.0-pre.1
    - 1.7.0-pre.2
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.8.6
    - 1.8.7
    - 1.8.8
    compatible:
    - 1.6.4
    - 1.6.5
    - 1.7.0-pre.1
    - 1.7.0-pre.2
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.8.6
    - 1.8.7
    - 1.8.8
    verified: 1.6.5
  dependencies:
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  keywords:
  - unity
  - animation
  - editor
  - timeline
  - tools
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637789039180000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git
    revision: d7e1eb6805737974459309b7d6e7db58635dd167
    path: 
  unityLifecycle:
    version: 1.6.4
    nextVersion: 
- packageId: com.unity.ugui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.ugui@1.0.0
  assetPath: Packages/com.unity.ugui
  name: com.unity.ugui
  displayName: Unity UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Unity UI is a set of tools for developing user interfaces for games
    and applications. It is a GameObject-based UI system that uses Components and
    the Game View to arrange, position, and style user interfaces. \u200B You cannot
    use Unity UI to create or change user interfaces in the Unity Editor."
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - UI
  - ugui
  - Unity UI
  - Canvas
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.ai@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.ai@1.0.0
  assetPath: Packages/com.unity.modules.ai
  name: com.unity.modules.ai
  displayName: AI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AI module implements the path finding features in Unity. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AIModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.androidjni@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.androidjni@1.0.0
  assetPath: Packages/com.unity.modules.androidjni
  name: com.unity.modules.androidjni
  displayName: Android JNI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'AndroidJNI module allows you to call Java code. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.animation@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.animation@1.0.0
  assetPath: Packages/com.unity.modules.animation
  name: com.unity.modules.animation
  displayName: Animation
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Animation module implements Unity''s animation system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.assetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.assetbundle@1.0.0
  assetPath: Packages/com.unity.modules.assetbundle
  name: com.unity.modules.assetbundle
  displayName: Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AssetBundle module implements the AssetBundle class and related
    APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.audio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.audio@1.0.0
  assetPath: Packages/com.unity.modules.audio
  name: com.unity.modules.audio
  displayName: Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Audio module implements Unity''s audio system. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.cloth@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.cloth@1.0.0
  assetPath: Packages/com.unity.modules.cloth
  name: com.unity.modules.cloth
  displayName: Cloth
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Cloth module implements cloth physics simulation through the
    Cloth component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ClothModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.director@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.director@1.0.0
  assetPath: Packages/com.unity.modules.director
  name: com.unity.modules.director
  displayName: Director
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Director module implements the PlayableDirector class. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.DirectorModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.imageconversion@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.imageconversion@1.0.0
  assetPath: Packages/com.unity.modules.imageconversion
  name: com.unity.modules.imageconversion
  displayName: Image Conversion
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ImageConversion module implements the ImageConversion class which
    provides helper methods to convert images from and to PNG, JPEG or EXR formats.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.imgui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.imgui@1.0.0
  assetPath: Packages/com.unity.modules.imgui
  name: com.unity.modules.imgui
  displayName: IMGUI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The IMGUI module provides Unity''s immediate mode GUI solution for
    creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.jsonserialize@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.jsonserialize@1.0.0
  assetPath: Packages/com.unity.modules.jsonserialize
  name: com.unity.modules.jsonserialize
  displayName: JSONSerialize
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The JSONSerialize module provides the JsonUtility class which lets
    you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.particlesystem@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.particlesystem@1.0.0
  assetPath: Packages/com.unity.modules.particlesystem
  name: com.unity.modules.particlesystem
  displayName: Particle System
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ParticleSystem module implements Unity''s Particle System. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.physics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.physics@1.0.0
  assetPath: Packages/com.unity.modules.physics
  name: com.unity.modules.physics
  displayName: Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics module implements 3D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.physics2d@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.physics2d@1.0.0
  assetPath: Packages/com.unity.modules.physics2d
  name: com.unity.modules.physics2d
  displayName: Physics 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics2d module implements 2D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.screencapture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.screencapture@1.0.0
  assetPath: Packages/com.unity.modules.screencapture
  name: com.unity.modules.screencapture
  displayName: Screen Capture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ScreenCapture module provides functionality to take screen shots
    using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.terrain@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.terrain@1.0.0
  assetPath: Packages/com.unity.modules.terrain
  name: com.unity.modules.terrain
  displayName: Terrain
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Terrain module implements Unity''s Terrain rendering engine available
    through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.terrainphysics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.terrainphysics@1.0.0
  assetPath: Packages/com.unity.modules.terrainphysics
  name: com.unity.modules.terrainphysics
  displayName: Terrain Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The TerrainPhysics module connects the Terrain and Physics modules
    by implementing the TerrainCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainPhysicsModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.tilemap@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.tilemap@1.0.0
  assetPath: Packages/com.unity.modules.tilemap
  name: com.unity.modules.tilemap
  displayName: Tilemap
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Tilemap module implements the Tilemap class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.ui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.ui@1.0.0
  assetPath: Packages/com.unity.modules.ui
  name: com.unity.modules.ui
  displayName: UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UI module implements basic components required for Unity''s UI
    system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.uielements@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.uielements@1.0.0
  assetPath: Packages/com.unity.modules.uielements
  name: com.unity.modules.uielements
  displayName: UIElements
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UIElements module implements the UIElements retained mode UI
    framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.uielementsnative
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.uielementsnative
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.umbra@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.umbra@1.0.0
  assetPath: Packages/com.unity.modules.umbra
  name: com.unity.modules.umbra
  displayName: Umbra
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Umbra module implements Unity''s occlusion culling system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.UmbraModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.unityanalytics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.unityanalytics@1.0.0
  assetPath: Packages/com.unity.modules.unityanalytics
  name: com.unity.modules.unityanalytics
  displayName: Unity Analytics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityAnalytics module implements APIs required to use Unity Analytics.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityAnalyticsModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.unitywebrequest@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.unitywebrequest@1.0.0
  assetPath: Packages/com.unity.modules.unitywebrequest
  name: com.unity.modules.unitywebrequest
  displayName: Unity Web Request
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequest module lets you communicate with http services.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.unitywebrequestassetbundle@1.0.0
  assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
  name: com.unity.modules.unitywebrequestassetbundle
  displayName: Unity Web Request Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
    class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.unitywebrequestaudio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.unitywebrequestaudio@1.0.0
  assetPath: Packages/com.unity.modules.unitywebrequestaudio
  name: com.unity.modules.unitywebrequestaudio
  displayName: Unity Web Request Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
    class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.unitywebrequesttexture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.unitywebrequesttexture@1.0.0
  assetPath: Packages/com.unity.modules.unitywebrequesttexture
  name: com.unity.modules.unitywebrequesttexture
  displayName: Unity Web Request Texture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
    class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.unitywebrequestwww@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.unitywebrequestwww@1.0.0
  assetPath: Packages/com.unity.modules.unitywebrequestwww
  name: com.unity.modules.unitywebrequestwww
  displayName: Unity Web Request WWW
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestWWW module implements the legacy WWW lets you
    communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.vehicles@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.vehicles@1.0.0
  assetPath: Packages/com.unity.modules.vehicles
  name: com.unity.modules.vehicles
  displayName: Vehicles
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Vehicles module implements vehicle physics simulation through
    the WheelCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VehiclesModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.video@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.video@1.0.0
  assetPath: Packages/com.unity.modules.video
  name: com.unity.modules.video
  displayName: Video
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Video module lets you play back video files in your content.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VideoModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.vr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.vr@1.0.0
  assetPath: Packages/com.unity.modules.vr
  name: com.unity.modules.vr
  displayName: VR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The VR module implements support for virtual reality devices in Unity.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VRModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.wind@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.wind@1.0.0
  assetPath: Packages/com.unity.modules.wind
  name: com.unity.modules.wind
  displayName: Wind
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Wind module implements the WindZone component which can affect
    terrain rendering and particle simulations. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.WindModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.xr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.xr@1.0.0
  assetPath: Packages/com.unity.modules.xr
  name: com.unity.modules.xr
  displayName: XR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The XR module contains the VR and AR related platform support functionality.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.XRModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.modules.subsystems@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.subsystems@1.0.0
  assetPath: Packages/com.unity.modules.subsystems
  name: com.unity.modules.subsystems
  displayName: Subsystems
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Subsystem module contains the definitions and runtime support
    for general subsystems in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.SubsystemsModule.html'
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
- packageId: com.unity.modules.uielementsnative@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.modules.uielementsnative@1.0.0
  assetPath: Packages/com.unity.modules.uielementsnative
  name: com.unity.modules.uielementsnative
  displayName: UIElements Native
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
- packageId: com.unity.ext.nunit@1.0.6
  testable: 0
  isDirectDependency: 0
  version: 1.0.6
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.ext.nunit@1.0.6
  assetPath: Packages/com.unity.ext.nunit
  name: com.unity.ext.nunit
  displayName: Custom NUnit
  author:
    name: 
    email: 
    url: 
  category: Libraries
  type: 
  description: Custom version of the nunit package build to work with Unity. Used
    by the Unity Test Framework.
  status: 0
  errors: []
  versions:
    all:
    - 0.1.5-preview
    - 0.1.6-preview
    - 0.1.9-preview
    - 1.0.0
    - 1.0.5
    - 1.0.6
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    compatible:
    - 1.0.6
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    verified: 1.0.6
  dependencies: []
  resolvedDependencies: []
  keywords:
  - nunit
  - unittest
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637429759280000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ext.nunit.git
    revision: 29ea4d6504a5f58fb3a6934db839aa80ae6d9d88
    path: 
  unityLifecycle:
    version: 1.0.6
    nextVersion: 
- packageId: com.unity.scriptablebuildpipeline@1.20.1
  testable: 0
  isDirectDependency: 0
  version: 1.20.1
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.scriptablebuildpipeline@1.20.1
  assetPath: Packages/com.unity.scriptablebuildpipeline
  name: com.unity.scriptablebuildpipeline
  displayName: Scriptable Build Pipeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Scriptable Build Pipeline moves the asset bundle build pipeline
    to C#.  Use the pre-defined build flows, or create your own using the divided
    up APIs.  This system improves build time, fixes incremental build, and provides
    greater flexibility.
  status: 0
  errors: []
  versions:
    all:
    - 0.0.5-preview
    - 0.0.6-preview
    - 0.0.8-preview
    - 0.0.9-preview
    - 0.0.10-preview
    - 0.0.14-preview
    - 0.0.15-preview
    - 0.1.0-preview
    - 0.2.0-preview
    - 1.0.1-preview
    - 1.1.0-preview
    - 1.1.1-preview
    - 1.2.1-preview
    - 1.3.5-preview
    - 1.4.1-preview
    - 1.5.0-preview
    - 1.5.1
    - 1.5.2
    - 1.5.4
    - 1.5.6
    - 1.5.10
    - 1.6.3-preview
    - 1.6.4-preview
    - 1.6.5-preview
    - 1.7.2
    - 1.7.3
    - 1.8.2
    - 1.8.4
    - 1.8.6
    - 1.9.0
    - 1.10.0
    - 1.11.1
    - 1.11.2
    - 1.12.0
    - 1.13.1
    - 1.14.0
    - 1.14.1
    - 1.15.1
    - 1.15.2
    - 1.16.1
    - 1.17.0
    - 1.18.0
    - 1.19.0
    - 1.19.1
    - 1.19.2
    - 1.19.3
    - 1.19.4
    - 1.19.5
    - 1.19.6
    - 1.20.1
    - 1.20.2
    - 1.21.0
    - 1.21.1
    - 1.21.2
    - 1.21.3
    - 1.21.5
    - 1.21.7
    - 1.21.8
    - 1.21.9
    - 1.21.20
    - 1.21.21
    - 1.21.22
    - 1.21.23
    - 1.21.24
    - 1.21.25
    - 1.22.1
    - 1.22.2
    - 1.22.4
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.2
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.2.4
    - 2.2.11
    - 2.3.0
    - 2.3.1
    - 2.3.2
    - 2.3.3
    - 2.3.4
    - 2.3.5
    - 2.3.6
    - 2.3.7
    - 2.3.8
    - 2.4.0
    compatible:
    - 1.20.1
    - 1.20.2
    - 1.21.0
    - 1.21.1
    - 1.21.2
    - 1.21.3
    - 1.21.5
    - 1.21.7
    - 1.21.8
    - 1.21.9
    - 1.21.20
    - 1.21.21
    - 1.21.22
    - 1.21.23
    - 1.21.24
    - 1.21.25
    - 1.22.1
    - 1.22.2
    - 1.22.4
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.2
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.2.4
    verified: 1.20.2
  dependencies: []
  resolvedDependencies: []
  keywords:
  - build
  - bundle
  - bundles
  - assetbundles
  - cache
  - server
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637874640850000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/Addressables.git
    revision: f2d9aac4eb254f268ff4e23f800ccc30c53dbd75
    path: 
  unityLifecycle:
    version: 1.20.1
    nextVersion: 
- packageId: com.unity.mathematics@1.2.6
  testable: 0
  isDirectDependency: 0
  version: 1.2.6
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.mathematics@1.2.6
  assetPath: Packages/com.unity.mathematics
  name: com.unity.mathematics
  displayName: Mathematics
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Unity's C# SIMD math library providing vector types and math functions
    with a shader like syntax.
  status: 0
  errors: []
  versions:
    all:
    - 0.0.12-preview.2
    - 0.0.12-preview.5
    - 0.0.12-preview.8
    - 0.0.12-preview.10
    - 0.0.12-preview.11
    - 0.0.12-preview.13
    - 0.0.12-preview.17
    - 0.0.12-preview.19
    - 0.0.12-preview.20
    - 1.0.0-preview.1
    - 1.0.1
    - 1.1.0-preview.1
    - 1.1.0
    - 1.2.1
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.3.1
    - 1.3.2
    compatible:
    - 1.2.6
    - 1.3.1
    - 1.3.2
    verified: 1.2.6
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637841876140000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/Unity.Mathematics.git
    revision: f110c8c230d253654afed153569030a587cc7557
    path: 
  unityLifecycle:
    version: 1.2.6
    nextVersion: 
- packageId: com.unity.2d.common@6.0.6
  testable: 0
  isDirectDependency: 0
  version: 6.0.6
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.2d.common@6.0.6
  assetPath: Packages/com.unity.2d.common
  name: com.unity.2d.common
  displayName: 2D Common
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Common is a package that contains shared functionalities that are
    used by most of the other 2D packages.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.9-preview.1
    - 1.0.9-preview.2
    - 1.0.10-preview
    - 1.0.11-preview.1
    - 1.1.0-preview.1
    - 1.1.0-preview.2
    - 1.2.0-preview.1
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.1
    - 2.1.2
    - 3.0.0
    - 3.0.1
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.0.3
    - 4.0.4
    - 4.1.0
    - 4.2.0
    - 4.2.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.0
    - 6.0.0-pre.2
    - 6.0.0-pre.3
    - 6.0.0-pre.4
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.6
    - 6.0.7
    - 6.0.8
    - 6.1.0
    - 7.0.0-pre.3
    - 7.0.0-pre.4
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 8.0.0-pre.1
    - 8.0.0-pre.2
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.1.0
    - 9.0.0-pre.1
    - 9.0.0-pre.2
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.0.5
    - 9.0.6
    - 9.0.7
    - 9.1.0
    - 10.0.0
    - 11.0.0
    compatible:
    - 6.0.6
    - 6.0.7
    - 6.0.8
    - 6.1.0
    verified: 6.0.8
  dependencies:
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.1.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.burst
    version: 1.5.1
  resolvedDependencies:
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.2.6
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.uielementsnative
    version: 1.0.0
  - name: com.unity.burst
    version: 1.6.6
  keywords:
  - 2d
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638035874770000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: aebc9b022b1d3ecdcc734e0ee8f139df3f3fe3dd
    path: 
  unityLifecycle:
    version: 6.0.6
    nextVersion: 
- packageId: com.unity.2d.path@5.0.2
  testable: 0
  isDirectDependency: 0
  version: 5.0.2
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.2d.path@5.0.2
  assetPath: Packages/com.unity.2d.path
  name: com.unity.2d.path
  displayName: 2D Path
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: "2D Path provides tooling to edit shapes (polygons and B\xE9zier splines)
    in EditorWindows and the SceneView."
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0-preview.1
    - 1.0.0-preview.4
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.6
    - 2.1.0
    - 2.1.1
    - 3.0.0
    - 3.0.1
    - 3.0.2
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.0
    - 5.0.1
    - 5.0.2
    compatible:
    - 5.0.2
    verified: 5.0.2
  dependencies: []
  resolvedDependencies: []
  keywords:
  - 2d
  - path
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637817447940000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: 41fd674f8e4b2265bdbc50291dced1bd86c590ee
    path: 
  unityLifecycle:
    version: 5.0.2
    nextVersion: 
- packageId: com.unity.2d.animation@7.0.9
  testable: 0
  isDirectDependency: 0
  version: 7.0.9
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.2d.animation@7.0.9
  assetPath: Packages/com.unity.2d.animation
  name: com.unity.2d.animation
  displayName: 2D Animation
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Animation provides all the necessary tooling and runtime components
    for skeletal animation using Sprites.
  status: 0
  errors: []
  versions:
    all:
    - 1.0.15-preview.1
    - 1.0.15-preview.2
    - 1.0.15-preview.3
    - 1.0.15-preview.4
    - 1.0.15-preview.5
    - 1.0.16-preview
    - 1.0.16-preview.1
    - 1.0.16-preview.2
    - 2.0.0-preview.1
    - 2.0.0-preview.2
    - 2.0.0-preview.3
    - 2.1.0-preview.1
    - 2.1.0-preview.2
    - 2.1.0-preview.4
    - 2.1.0-preview.5
    - 2.1.0-preview.7
    - 2.2.0-preview.1
    - 2.2.0-preview.4
    - 2.2.0-preview.5
    - 2.2.1-preview.1
    - 2.2.1-preview.2
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.8
    - 3.1.0
    - 3.1.1
    - 3.2.1
    - 3.2.2
    - 3.2.3
    - 3.2.4
    - 3.2.5
    - 3.2.6
    - 3.2.9
    - 3.2.10
    - 3.2.11
    - 3.2.13
    - 3.2.14
    - 3.2.15
    - 3.2.16
    - 3.2.17
    - 3.2.18
    - 4.0.0
    - 4.0.1
    - 4.1.0
    - 4.1.1
    - 4.2.1
    - 4.2.2
    - 4.2.3
    - 4.2.4
    - 4.2.5
    - 4.2.6
    - 4.2.8
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.0.3
    - 5.0.4
    - 5.0.5
    - 5.0.6
    - 5.0.7
    - 5.0.8
    - 5.0.9
    - 5.0.10
    - 5.1.0
    - 5.1.1
    - 5.2.0
    - 5.2.1
    - 5.2.3
    - 5.2.4
    - 5.2.6
    - 5.2.7
    - 6.0.0-pre.1
    - 6.0.0-pre.2
    - 6.0.1
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.7
    - 7.0.0-pre.2
    - 7.0.0-pre.3
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 7.0.4
    - 7.0.5
    - 7.0.6
    - 7.0.7
    - 7.0.8
    - 7.0.9
    - 7.0.10
    - 7.0.11
    - 7.0.12
    - 7.0.13
    - 7.1.0
    - 7.1.1
    - 7.1.2
    - 7.2.0
    - 8.0.0-pre.3
    - 8.0.0-pre.4
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.0.5
    - 9.0.0-pre.1
    - 9.0.0-pre.3
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.1.0
    - 9.1.1
    - 9.1.2
    - 9.1.3
    - 9.2.0
    - 10.0.0-pre.1
    - 10.0.0-pre.2
    - 10.0.0
    - 10.0.1
    - 10.0.2
    - 10.0.3
    - 10.1.0
    - 10.1.1
    - 10.1.2
    - 10.1.3
    - 10.1.4
    - 10.2.0
    - 11.0.0
    compatible:
    - 7.0.9
    - 7.0.10
    - 7.0.11
    - 7.0.12
    - 7.0.13
    - 7.1.0
    - 7.1.1
    - 7.1.2
    - 7.2.0
    verified: 7.0.13
  dependencies:
  - name: com.unity.2d.common
    version: 6.0.6
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 6.0.6
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.2.6
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.uielementsnative
    version: 1.0.0
  - name: com.unity.burst
    version: 1.6.6
  - name: com.unity.modules.animation
    version: 1.0.0
  keywords:
  - 2d
  - animation
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638035874810000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: aebc9b022b1d3ecdcc734e0ee8f139df3f3fe3dd
    path: 
  unityLifecycle:
    version: 7.0.9
    nextVersion: 
- packageId: com.unity.2d.sprite@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.2d.sprite@1.0.0
  assetPath: Packages/com.unity.2d.sprite
  name: com.unity.2d.sprite
  displayName: 2D Sprite
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: Use Unity Sprite Editor Window to create and edit Sprite asset properties
    like pivot, borders and Physics shape
  status: 0
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    verified: 1.0.0
  dependencies: []
  resolvedDependencies: []
  keywords:
  - 2d
  - sprite
  - sprite editor window
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
- packageId: com.unity.burst@1.6.6
  testable: 0
  isDirectDependency: 0
  version: 1.6.6
  source: 1
  resolvedPath: G:\GAME_ZD\Brotato_LegionSurvivors\Library\PackageCache\com.unity.burst@1.6.6
  assetPath: Packages/com.unity.burst
  name: com.unity.burst
  displayName: Burst
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Burst is a compiler that translates from IL/.NET bytecode to highly
    optimized native code using LLVM.
  status: 0
  errors: []
  versions:
    all:
    - 0.2.4-preview.5
    - 0.2.4-preview.7
    - 0.2.4-preview.11
    - 0.2.4-preview.12
    - 0.2.4-preview.13
    - 0.2.4-preview.14
    - 0.2.4-preview.15
    - 0.2.4-preview.16
    - 0.2.4-preview.17
    - 0.2.4-preview.18
    - 0.2.4-preview.19
    - 0.2.4-preview.20
    - 0.2.4-preview.21
    - 0.2.4-preview.22
    - 0.2.4-preview.23
    - 0.2.4-preview.24
    - 0.2.4-preview.25
    - 0.2.4-preview.30
    - 0.2.4-preview.31
    - 0.2.4-preview.33
    - 0.2.4-preview.34
    - 0.2.4-preview.37
    - 0.2.4-preview.41
    - 0.2.4-preview.45
    - 0.2.4-preview.48
    - 0.2.4-preview.50
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.0-preview.4
    - 1.1.1
    - 1.1.2
    - 1.1.3-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.5
    - 1.2.0-preview.6
    - 1.2.0-preview.8
    - 1.2.0-preview.9
    - 1.2.0-preview.10
    - 1.2.0-preview.11
    - 1.2.0-preview.12
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.3.0-preview.1
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.4
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0-preview.8
    - 1.3.0-preview.9
    - 1.3.0-preview.10
    - 1.3.0-preview.11
    - 1.3.0-preview.12
    - 1.3.0-preview.13
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0-pre.1
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.4
    - 1.4.0-preview.5
    - 1.4.1-pre.1
    - 1.4.1-pre.2
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4-preview.1
    - 1.4.4-preview.2
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.4.9
    - 1.4.11
    - 1.5.0-pre.3
    - 1.5.0-pre.4
    - 1.5.0-pre.5
    - 1.5.0
    - 1.5.1
    - 1.5.2
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6-preview.1
    - 1.5.6
    - 1.6.0-pre.2
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.6.6
    - 1.7.0-pre.1
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.7
    - 1.8.8
    - 1.8.9
    - 1.8.10
    - 1.8.11
    - 1.8.12
    - 1.8.13
    - 1.8.14
    - 1.8.15
    - 1.8.16
    - 1.8.17
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    compatible:
    - 1.6.6
    - 1.7.0-pre.1
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.7
    - 1.8.8
    - 1.8.9
    - 1.8.10
    - 1.8.11
    - 1.8.12
    - 1.8.13
    - 1.8.14
    - 1.8.15
    - 1.8.16
    - 1.8.17
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    verified: 1.6.6
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.2.6
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.cn
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637902875830000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/burst.git
    revision: 21ee3d01c6a374f6b01106f9eb6f1b43e113b1a3
    path: 
  unityLifecycle:
    version: 1.6.6
    nextVersion: 
