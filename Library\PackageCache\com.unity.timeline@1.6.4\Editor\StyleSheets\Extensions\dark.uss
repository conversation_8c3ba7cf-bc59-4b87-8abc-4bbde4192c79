.Font-Clip {
    /* GUIState.textColor */
    color: rgb(255, 255, 255);
}

.Icon-Activation {
    /* GUIStyle.fixedWidth */
    width: 12;

    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineActivation.png");
}

.trackRecordButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackRecordButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackRecordButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackRecordButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackAvatarMaskButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackAvatarButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackAvatarMaskButton:checked {
     background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackAvatarButtonEnabled.png");
     -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.Icon-ClipIn {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineIconClipIn.png");
}

.Icon-ClipOut {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineIconClipOut.png");
}

.Icon-ClipSelected {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineClipGradientSelected.png");
}

.trackCurvesButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackCurvesButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackCurvesButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackCurvesButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.Icon-Endmarker {
    /* GUIState.textColor */
    color: rgb(61, 94, 152);
}

.MarkerItem {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineMarkerItemCollapsed.png");
}

.MarkerItem:checked {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineMarkerItem.png");
}

.MarkerItem:hover:focus:checked {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineMarkerItemSelected.png");
}

.SignalEmitter {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineSignalCollapsed.png");
}

.SignalEmitter:checked {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TimelineSignal.png");
}

.SignalEmitter:hover:focus:checked {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineSignalSelected.png");
}

.trackCollapseMarkerButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackMarkerButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackCollapseMarkerButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackMarkerButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.Icon-ExtrapolationContinue {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineContinue.png");

    /* GUIStyle.contentOffset */
    -unity-content-offset: -5 0;
}

.Icon-ExtrapolationHold {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineHold.png");
}

.Icon-ExtrapolationLoop {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineLoop.png");
}

.Icon-ExtrapolationPingPong {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelinePingPong.png");
}

.Icon-Foldout {
    /* GUIState.background */
    background-image: resource("Builtin Skins/DarkSkin/Images/IN foldout.png");

    /* GUIState.scaledBackgrounds */
    -unity-scaled-backgrounds: resource("Builtin Skins/DarkSkin/Images/IN <EMAIL>");
}

/* GUIStyle.active */
.Icon-Foldout:hover:active {
    /* GUIState.background */
    background-image: resource("Builtin Skins/DarkSkin/Images/IN foldout on.png");

    /* GUIState.scaledBackgrounds */
    -unity-scaled-backgrounds: resource("Builtin Skins/DarkSkin/Images/<NAME_EMAIL>");
}

/* GUIStyle.onActive */
.Icon-Foldout:hover:active:checked {
    /* GUIState.background */
    background-image: resource("Builtin Skins/DarkSkin/Images/IN foldout on.png");

    /* GUIState.scaledBackgrounds */
    -unity-scaled-backgrounds: resource("Builtin Skins/DarkSkin/Images/<NAME_EMAIL>");
}

/* GUIStyle.onNormal */
.Icon-Foldout:checked {
    /* GUIState.background */
    background-image: resource("Builtin Skins/DarkSkin/Images/IN foldout on.png");

    /* GUIState.scaledBackgrounds */
    -unity-scaled-backgrounds: resource("Builtin Skins/DarkSkin/Images/<NAME_EMAIL>");
}

.Icon-InfiniteTrack {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineInfiniteTrackNoShadow.png");
}

.Icon-Keyframe {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineKeyframe.png");
}

.trackLockOverlay {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineDisabledBackground.png");
}

.trackMuteButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackMuteButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackMuteButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackMuteButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.trackLockButton {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackLockButtonDisabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
 }

.trackLockButton:checked {
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/d_TrackLockButtonEnabled.png");
    -unity-scaled-backgrounds: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/Icons/<EMAIL>");
}

.Icon-Options {
    /* GUIState.background */
    background-image: resource("Icons/d__Popup.png");
}

.Icon-PlayAreaEnd {
    /* GUIState.textColor */
    color: rgb(187, 189, 191);

    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineEndPlayback.png");
}

.Icon-PlayAreaStart {
    /* GUIState.textColor */
    color: rgb(187, 189, 191);

    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineStartPlayback.png");
}

.Icon-Playrange {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelinePlayRange.png");
}

.Icon-TimeCursor {
    /* GUIState.background */
    background-image: resource("Packages/com.unity.timeline/Editor/StyleSheets/Images/DarkSkin/TimelineTimeCursor.png");
}

.Icon-TrackOptions {
    /* GUIStyle.imagePosition */
    -unity-image-position: image-only;

    /* GUIState.background */
    background-image: resource("Builtin Skins/DarkSkin/Images/pane options.png");

    /* GUIState.scaledBackgrounds */
    -unity-scaled-backgrounds: resource("Builtin Skins/DarkSkin/Images/pane <EMAIL>");
}

.sequenceGroupFont {
    /* GUIState.textColor */
    color: rgb(204, 204, 204);
}

.sequenceSwitcher{
    background-color: rgb(51, 51, 51);
}

.trackButtonSuite {
    background-color: rgba(40, 40, 40, 0.75);
}
