-target:library
-out:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_2021_3_21
-define:UNITY_2021_3
-define:UNITY_2021
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_UNET
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_COLLAB
-define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_UNET
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_RUNTIME_PERMISSIONS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:PLATFORM_ANDROID
-define:TEXTCORE_1_0_OR_NEWER
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_IG
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TextMeshPro
-define:Vertical_Screen
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Excel.dll"
-r:"Assets/Plugins/ICSharpCode.SharpZipLib.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIWidgetsModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll"
-r:"G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll"
-r:"G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll"
-r:"G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll"
-r:"G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.Unsafe.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Cinemachine.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/com.unity.cinemachine.editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/ParticleImage.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/ParticleImage.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/PsdPlugin.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UIEffect.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity Timer.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Animation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Animation.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Common.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Common.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.IK.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.IK.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Path.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.PixelPerfect.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.PixelPerfect.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Psdimporter.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.SpriteShape.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.SpriteShape.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.2D.Tilemap.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Addressables.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Addressables.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InternalAPIEditorBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InternalAPIEngineBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.ResourceManager.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.ScriptableBuildPipeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.ScriptableBuildPipeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VSCode.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEditor.CacheServer.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.ref.dll"
"Assets/3rd/Script/BpAndroidTools.cs"
"Assets/3rd/Script/DevConfig.cs"
"Assets/3rd/Script/Reporter.cs"
"Assets/3rd/Script/TalkingDataSDKHost.cs"
"Assets/3rd/UmengSDK/TestCase/TestCase.cs"
"Assets/3rd/UmengSDK/UmengGameAnalytics/Example/Example.cs"
"Assets/3rd/UmengSDK/UmengGameAnalytics/Scripts/Analytics.cs"
"Assets/3rd/UmengSDK/UmengGameAnalytics/Scripts/DplusAgent.cs"
"Assets/3rd/UmengSDK/UmengGameAnalytics/Scripts/GA.cs"
"Assets/3rd/UmengSDK/UmengGameAnalytics/Scripts/SimpleJSON.cs"
"Assets/3rd/UmengSDK/UmengGameAnalytics/Scripts/UmengAndroidLifeCycleCallBack.cs"
"Assets/3rd/UmengSDK/UmengGameAnalytics/Scripts/UmengManager.cs"
"Assets/3rd/UmengSDK/UmengSDKHost.cs"
"Assets/ADSystem/Plugins/LitJson/IJsonWrapper.cs"
"Assets/ADSystem/Plugins/LitJson/JsonData.cs"
"Assets/ADSystem/Plugins/LitJson/JsonException.cs"
"Assets/ADSystem/Plugins/LitJson/JsonMapper.cs"
"Assets/ADSystem/Plugins/LitJson/JsonMockWrapper.cs"
"Assets/ADSystem/Plugins/LitJson/JsonReader.cs"
"Assets/ADSystem/Plugins/LitJson/JsonWriter.cs"
"Assets/ADSystem/Plugins/LitJson/Lexer.cs"
"Assets/ADSystem/Plugins/LitJson/Netstandard15Polyfill.cs"
"Assets/ADSystem/Plugins/LitJson/ParserToken.cs"
"Assets/ADSystem/Scripts/ADBridge/ADUnion/ADUnionBannerADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/ADUnion/ADUnionInterstitialADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/ADUnion/ADUnionVideoADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/CSJAD/CSJBannerADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/CSJAD/CSJFullscreenADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/CSJAD/CSJInterstitialADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/CSJAD/CSJNewInterstitialADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/CSJAD/CSJRewardADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/FaceBook/FBBanner.cs"
"Assets/ADSystem/Scripts/ADBridge/FaceBook/FBInterstitial.cs"
"Assets/ADSystem/Scripts/ADBridge/FaceBook/FBReward.cs"
"Assets/ADSystem/Scripts/ADBridge/GDTAD/GDTBannerADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/GDTAD/GDTFullScreenADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/GDTAD/GDTInterADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/GDTAD/GDTRewardADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/GoogleAdMob/GADBanner.cs"
"Assets/ADSystem/Scripts/ADBridge/GoogleAdMob/GADInterstitial.cs"
"Assets/ADSystem/Scripts/ADBridge/GoogleAdMob/GADNative.cs"
"Assets/ADSystem/Scripts/ADBridge/GoogleAdMob/GADReward.cs"
"Assets/ADSystem/Scripts/ADBridge/GoogleAdMob/GADSplash.cs"
"Assets/ADSystem/Scripts/ADBridge/HuaWei/HuaweiBannerADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/HuaWei/HuaweiInterstitialADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/HuaWei/HuaweiRewardADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/HuaWei/HuaweiSplashADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/KSAD/KSFullScreenADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/KSAD/KSInterstitialADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/KSAD/KSRewardADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/LGSDK/LGFullscreenADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/LGSDK/LGRewardADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/MetaAd/MetaAdBannerADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/MetaAd/MetaAdFullscreenMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/MetaAd/MetaAdInterstitialADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/MetaAd/MetaAdVideoADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/Stark/DYBanner.cs"
"Assets/ADSystem/Scripts/ADBridge/Stark/DYInterstitial.cs"
"Assets/ADSystem/Scripts/ADBridge/Stark/DYMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/Stark/DYReward.cs"
"Assets/ADSystem/Scripts/ADBridge/WXAD/WXBanner.cs"
"Assets/ADSystem/Scripts/ADBridge/WXAD/WXInterstitial.cs"
"Assets/ADSystem/Scripts/ADBridge/WXAD/WXReward.cs"
"Assets/ADSystem/Scripts/ADBridge/XIAOMI/XMBannerADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/XIAOMI/XMFullScreenADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/XIAOMI/XMInterstitialADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/XIAOMI/XMNativeADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/XIAOMI/XMSplashADMgr.cs"
"Assets/ADSystem/Scripts/ADBridge/XIAOMI/XMVideoADMgr.cs"
"Assets/ADSystem/Scripts/ADMgr/ADMgr.cs"
"Assets/ADSystem/Scripts/ADMgr/ADMgrInfo.cs"
"Assets/ADSystem/Scripts/ADMgr/ADMgrListener.cs"
"Assets/ADSystem/Scripts/ADMgr/CADManager.cs"
"Assets/ADSystem/Scripts/ADMgr/IADManager.cs"
"Assets/ADSystem/Scripts/Advertisement.cs"
"Assets/ADSystem/Scripts/ClientStaticConfig.cs"
"Assets/ADSystem/Scripts/Common/SingletonBehavior.cs"
"Assets/ADSystem/Scripts/Common/SingletonComponent.cs"
"Assets/Common/Scripts/DontDestroy.cs"
"Assets/Common/Scripts/FirstScene.cs"
"Assets/Common/Scripts/Logo.cs"
"Assets/Common/Scripts/NetCheck.cs"
"Assets/Common/UnityTimer-1.1.0/Example/TestTimerBehaviour.cs"
"Assets/DragonBones/Scripts/3rdParty/MiniJSON.cs"
"Assets/DragonBones/Scripts/animation/Animation.cs"
"Assets/DragonBones/Scripts/animation/AnimationState.cs"
"Assets/DragonBones/Scripts/animation/BaseTimelineState.cs"
"Assets/DragonBones/Scripts/animation/IAnimatable.cs"
"Assets/DragonBones/Scripts/animation/TimelineState.cs"
"Assets/DragonBones/Scripts/animation/WorldClock.cs"
"Assets/DragonBones/Scripts/armature/Armature.cs"
"Assets/DragonBones/Scripts/armature/Bone.cs"
"Assets/DragonBones/Scripts/armature/Constraint.cs"
"Assets/DragonBones/Scripts/armature/DeformVertices.cs"
"Assets/DragonBones/Scripts/armature/IArmatureProxy.cs"
"Assets/DragonBones/Scripts/armature/Slot.cs"
"Assets/DragonBones/Scripts/armature/TransformObject.cs"
"Assets/DragonBones/Scripts/core/BaseObject.cs"
"Assets/DragonBones/Scripts/core/DragonBones.cs"
"Assets/DragonBones/Scripts/event/EventObject.cs"
"Assets/DragonBones/Scripts/event/IEventDispatcher.cs"
"Assets/DragonBones/Scripts/factory/BaseFactory.cs"
"Assets/DragonBones/Scripts/geom/ColorTransform.cs"
"Assets/DragonBones/Scripts/geom/Matrix.cs"
"Assets/DragonBones/Scripts/geom/Point.cs"
"Assets/DragonBones/Scripts/geom/Rectangle.cs"
"Assets/DragonBones/Scripts/geom/Transform.cs"
"Assets/DragonBones/Scripts/model/AnimationConfig.cs"
"Assets/DragonBones/Scripts/model/AnimationData.cs"
"Assets/DragonBones/Scripts/model/ArmatureData.cs"
"Assets/DragonBones/Scripts/model/BoundingBoxData.cs"
"Assets/DragonBones/Scripts/model/CanvasData.cs"
"Assets/DragonBones/Scripts/model/ConstraintData.cs"
"Assets/DragonBones/Scripts/model/DisplayData.cs"
"Assets/DragonBones/Scripts/model/DragonBonesData.cs"
"Assets/DragonBones/Scripts/model/SkinData.cs"
"Assets/DragonBones/Scripts/model/TextureAtlasData.cs"
"Assets/DragonBones/Scripts/model/UserData.cs"
"Assets/DragonBones/Scripts/parser/BinaryDataParser.cs"
"Assets/DragonBones/Scripts/parser/BinaryDataReader.cs"
"Assets/DragonBones/Scripts/parser/BinaryDataWriter.cs"
"Assets/DragonBones/Scripts/parser/DataParser.cs"
"Assets/DragonBones/Scripts/parser/ObjectDataParser.cs"
"Assets/DragonBones/Scripts/unity/MeshBuffer.cs"
"Assets/DragonBones/Scripts/unity/UnityArmatureComponent.cs"
"Assets/DragonBones/Scripts/unity/UnityCombineMeshs.cs"
"Assets/DragonBones/Scripts/unity/UnityDragonBonesData.cs"
"Assets/DragonBones/Scripts/unity/UnityEventDispatcher.cs"
"Assets/DragonBones/Scripts/unity/UnityFactory.cs"
"Assets/DragonBones/Scripts/unity/UnitySlot.cs"
"Assets/DragonBones/Scripts/unity/UnityTextureAtlasData.cs"
"Assets/DragonBones/Scripts/unity/UnityUGUIDisplay.cs"
"Assets/I2/Localization/Examples/Common/Scripts/CallbackNotification.cs"
"Assets/I2/Localization/Examples/Common/Scripts/Example_ChangeLanguage.cs"
"Assets/I2/Localization/Examples/Common/Scripts/Example_LocalizedString.cs"
"Assets/I2/Localization/Examples/Common/Scripts/GlobalParametersExample.cs"
"Assets/I2/Localization/Examples/Common/Scripts/NGUI_LanguagePopup.cs"
"Assets/I2/Localization/Examples/Common/Scripts/RealTimeTranslation.cs"
"Assets/I2/Localization/Examples/Common/Scripts/RegisterBundlesManager.cs"
"Assets/I2/Localization/Examples/Common/Scripts/ToggleLanguage.cs"
"Assets/I2/Localization/Scripts/Configurables/PersistentStorage.cs"
"Assets/I2/Localization/Scripts/Configurables/SpecializationManager.cs"
"Assets/I2/Localization/Scripts/EventCallback.cs"
"Assets/I2/Localization/Scripts/Google/GoogleLanguages.cs"
"Assets/I2/Localization/Scripts/Google/GoogleTranslation.cs"
"Assets/I2/Localization/Scripts/Google/GoogleTranslation_Post.cs"
"Assets/I2/Localization/Scripts/Google/GoogleTranslation_Queries.cs"
"Assets/I2/Localization/Scripts/Google/SimpleJSON.cs"
"Assets/I2/Localization/Scripts/Google/TranslationJob.cs"
"Assets/I2/Localization/Scripts/Google/TranslationJob_GET.cs"
"Assets/I2/Localization/Scripts/Google/TranslationJob_Main.cs"
"Assets/I2/Localization/Scripts/Google/TranslationJob_POST.cs"
"Assets/I2/Localization/Scripts/Google/TranslationJob_WEB.cs"
"Assets/I2/Localization/Scripts/LanguageData.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSource.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSourceAsset.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSourceData.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSourceData_Assets.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSourceData_Export_CSV.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSourceData_Export_Google.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSourceData_Import_CSV.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSourceData_Import_Google.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSourceData_Languages.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSourceData_Misc.cs"
"Assets/I2/Localization/Scripts/LanguageSource/LanguageSourceData_Terms.cs"
"Assets/I2/Localization/Scripts/LocalizationReader.cs"
"Assets/I2/Localization/Scripts/Localize.cs"
"Assets/I2/Localization/Scripts/LocalizeDropdown.cs"
"Assets/I2/Localization/Scripts/Manager/LocalizationManager.cs"
"Assets/I2/Localization/Scripts/Manager/LocalizationManager_Language.cs"
"Assets/I2/Localization/Scripts/Manager/LocalizationManager_Parameters.cs"
"Assets/I2/Localization/Scripts/Manager/LocalizationManager_RTL.cs"
"Assets/I2/Localization/Scripts/Manager/LocalizationManager_Sources.cs"
"Assets/I2/Localization/Scripts/Manager/LocalizationManager_SystemLanguage.cs"
"Assets/I2/Localization/Scripts/Manager/LocalizationManager_Targets.cs"
"Assets/I2/Localization/Scripts/Manager/LocalizationManager_Translation.cs"
"Assets/I2/Localization/Scripts/Targets/ILocalizeTarget.cs"
"Assets/I2/Localization/Scripts/Targets/ILocalizeTargetDesc.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_2DToolKit_Label.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_2DToolKit_Sprite.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_NGUI_Label.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_NGUI_Sprite.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_NGUI_Texture.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_SVGImporter_Image.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_SVGImporter_Renderer.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_TextMeshPro_Label.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_TextMeshPro_UGUI.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_UnityStandard_AudioSource.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_UnityStandard_Child.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_UnityStandard_MeshRenderer.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_UnityStandard_Prefab.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_UnityStandard_SpriteRenderer.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_UnityStandard_TextMesh.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_UnityStandard_VideoPlayer.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_UnityUI_Image.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_UnityUI_RawImage.cs"
"Assets/I2/Localization/Scripts/Targets/LocalizeTarget_UnityUI_Text.cs"
"Assets/I2/Localization/Scripts/TermData.cs"
"Assets/I2/Localization/Scripts/Utils/AutoChangeCultureInfo.cs"
"Assets/I2/Localization/Scripts/Utils/CoroutineManager.cs"
"Assets/I2/Localization/Scripts/Utils/CustomLocalizeCallback.cs"
"Assets/I2/Localization/Scripts/Utils/HindiFixer.cs"
"Assets/I2/Localization/Scripts/Utils/I2Utils.cs"
"Assets/I2/Localization/Scripts/Utils/LocalizationParamsManager.cs"
"Assets/I2/Localization/Scripts/Utils/LocalizedString.cs"
"Assets/I2/Localization/Scripts/Utils/RegisterCallback_AllowSyncFromGoogle.cs"
"Assets/I2/Localization/Scripts/Utils/RegisterGlobalParameters.cs"
"Assets/I2/Localization/Scripts/Utils/ResourceManager.cs"
"Assets/I2/Localization/Scripts/Utils/RTLFixer.cs"
"Assets/I2/Localization/Scripts/Utils/SetLanguage.cs"
"Assets/I2/Localization/Scripts/Utils/SetLanguageDropdown.cs"
"Assets/I2/Localization/Scripts/Utils/StringObfuscator.cs"
"Assets/PaySystem/Scripts/CPayManager.cs"
"Assets/PaySystem/Scripts/GooglePay/GooglePaySDK.cs"
"Assets/PaySystem/Scripts/IOSPay/IOSIAPBridge.cs"
"Assets/PaySystem/Scripts/IPayManager.cs"
"Assets/PaySystem/Scripts/PayResult.cs"
"Assets/PaySystem/Scripts/PaySDKManager.cs"
"Assets/Script/TalkingDateMangger.cs"
"Assets/Scripts/AnimationEvent.cs"
"Assets/Scripts/AttackBehaviors/AttackBehavior.cs"
"Assets/Scripts/AttackBehaviors/ChargingAttackBehavior.cs"
"Assets/Scripts/AttackBehaviors/ShootingAttackBehavior.cs"
"Assets/Scripts/AttackBehaviors/SpawningAttackBehavior.cs"
"Assets/Scripts/Behavior.cs"
"Assets/Scripts/Channel/FaceBookBridge.cs"
"Assets/Scripts/Channel/HYKB/HYKBChannel.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/common/HykbContext.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/common/LogUtils.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/login/bean/HykbUser.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/login/HykbLogin.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/login/listener/HykbAntiListener.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/login/listener/HykbInitListener.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/login/listener/HykbLoginListener.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/login/listener/HykbUserListener.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/login/listener/HykbV2InitListener.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/single/UnionFcmListener.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/single/UnionFcmSDK.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/single/UnionFcmUser.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/single/UnionV2FcmListener.cs"
"Assets/Scripts/Channel/HYKB/m3839/sdk/ToastUtils.cs"
"Assets/Scripts/Channel/XiaomiLogin.cs"
"Assets/Scripts/Channel/XMIAPBridge.cs"
"Assets/Scripts/ClearImage.cs"
"Assets/Scripts/CutsceneManager.cs"
"Assets/Scripts/Data/AppearanceData.cs"
"Assets/Scripts/Data/BurningData.cs"
"Assets/Scripts/Data/DamageData.cs"
"Assets/Scripts/Data/Effect/AlienEyesEffect.cs"
"Assets/Scripts/Data/Effect/BurnChanceEffect.cs"
"Assets/Scripts/Data/Effect/BurningEffect.cs"
"Assets/Scripts/Data/Effect/ChanceStatDamageEffect.cs"
"Assets/Scripts/Data/Effect/ClassBonusEffect.cs"
"Assets/Scripts/Data/Effect/ConvertStatEffect.cs"
"Assets/Scripts/Data/Effect/Effect.cs"
"Assets/Scripts/Data/Effect/ExplodingEffect.cs"
"Assets/Scripts/Data/Effect/GainStatEveryKilledEnemiesEffect.cs"
"Assets/Scripts/Data/Effect/GainStatForEveryStatEffect.cs"
"Assets/Scripts/Data/Effect/HealingEffect.cs"
"Assets/Scripts/Data/Effect/HpCapEffect.cs"
"Assets/Scripts/Data/Effect/ItemExplodingEffect.cs"
"Assets/Scripts/Data/Effect/MixEffect.cs"
"Assets/Scripts/Data/Effect/NullEffect.cs"
"Assets/Scripts/Data/Effect/PacifistEffect.cs"
"Assets/Scripts/Data/Effect/ProjectileEffect.cs"
"Assets/Scripts/Data/Effect/ProjectilesOnHitEffect.cs"
"Assets/Scripts/Data/Effect/RemoveSpeedEffect.cs"
"Assets/Scripts/Data/Effect/ReplaceEffect.cs"
"Assets/Scripts/Data/Effect/SlowInZoneEffect.cs"
"Assets/Scripts/Data/Effect/StartingItemEffect.cs"
"Assets/Scripts/Data/Effect/StatEffect.cs"
"Assets/Scripts/Data/Effect/StatGainsModificationEffect.cs"
"Assets/Scripts/Data/Effect/StatWithMaxEffect.cs"
"Assets/Scripts/Data/Effect/StructureEffect.cs"
"Assets/Scripts/Data/Effect/TierEffect.cs"
"Assets/Scripts/Data/Effect/TurretEffect.cs"
"Assets/Scripts/Data/Effect/WeaponBonusEffect.cs"
"Assets/Scripts/Data/Effect/WeaponStackEffect.cs"
"Assets/Scripts/Data/LinkedStats.cs"
"Assets/Scripts/Data/PropbabilityData/ProbabilityData.cs"
"Assets/Scripts/Data/RunData.cs"
"Assets/Scripts/Data/Save/CurrentSettings.cs"
"Assets/Scripts/Data/Save/EquipData.cs"
"Assets/Scripts/Data/Save/GameSave.cs"
"Assets/Scripts/Data/Save/PlayerSave.cs"
"Assets/Scripts/Data/Save/SaveManager.cs"
"Assets/Scripts/Data/Scriptables/AffixCard.cs"
"Assets/Scripts/Data/Scriptables/CardBase.cs"
"Assets/Scripts/Data/Scriptables/CharacterCard.cs"
"Assets/Scripts/Data/Scriptables/CombineWeaponCard.cs"
"Assets/Scripts/Data/Scriptables/DifficultyCard.cs"
"Assets/Scripts/Data/Scriptables/EquipCard.cs"
"Assets/Scripts/Data/Scriptables/FollowerCard.cs"
"Assets/Scripts/Data/Scriptables/Inventory/Inventory.cs"
"Assets/Scripts/Data/Scriptables/LevelUpCard.cs"
"Assets/Scripts/Data/Scriptables/MonsterSettings.cs"
"Assets/Scripts/Data/Scriptables/PassLevelData.cs"
"Assets/Scripts/Data/Scriptables/PropCard.cs"
"Assets/Scripts/Data/Scriptables/Store/GameStoreData.cs"
"Assets/Scripts/Data/Scriptables/Store/PassData.cs"
"Assets/Scripts/Data/Scriptables/UpgradeData.cs"
"Assets/Scripts/Data/Scriptables/Wave/WaveData.cs"
"Assets/Scripts/Data/Scriptables/Wave/WaveGroupData.cs"
"Assets/Scripts/Data/Scriptables/Wave/WaveUnitData.cs"
"Assets/Scripts/Data/Scriptables/WeaponCard.cs"
"Assets/Scripts/Data/Scriptables/WeaponStats/MeleeWeaponStats.cs"
"Assets/Scripts/Data/Scriptables/WeaponStats/RangedWeaponStats.cs"
"Assets/Scripts/Data/Scriptables/WeaponStats/WeaponStats.cs"
"Assets/Scripts/Data/Scriptables/WeaponUpgradeData.cs"
"Assets/Scripts/Data/Singletons/GameData.cs"
"Assets/Scripts/Data/Singletons/ItemService.cs"
"Assets/Scripts/Data/Singletons/ProgressData.cs"
"Assets/Scripts/Data/Singletons/SignletonsDataBase.cs"
"Assets/Scripts/Data/StructureEffectData.cs"
"Assets/Scripts/Data/SuitCardEffect.cs"
"Assets/Scripts/Data/TempStats.cs"
"Assets/Scripts/Data/UnitStats.cs"
"Assets/Scripts/Data/UpdateSkin.cs"
"Assets/Scripts/EmojiText/EmojiText.cs"
"Assets/Scripts/Enemy/Boss.cs"
"Assets/Scripts/Enemy/BossState.cs"
"Assets/Scripts/Enemy/MonsterController.cs"
"Assets/Scripts/Enemy/Special/Buffer.cs"
"Assets/Scripts/Enemy/Special/CorruptedTree.cs"
"Assets/Scripts/Enemy/Special/Fly.cs"
"Assets/Scripts/Enemy/Special/HealerEnemy.cs"
"Assets/Scripts/Enemy/Special/InvokeBoss.cs"
"Assets/Scripts/Enemy/Special/LampreyEnemy.cs"
"Assets/Scripts/Enemy/Special/LootSpawner.cs"
"Assets/Scripts/Enemy/Special/MonkBoss.cs"
"Assets/Scripts/Enemy/Special/Predator.cs"
"Assets/Scripts/Enemy/Special/Pursuer.cs"
"Assets/Scripts/Enemy/Special/RhinoBoss.cs"
"Assets/Scripts/Enemy/Special/SlasherEgg.cs"
"Assets/Scripts/Enemy/Special/SpawnerEnemy.cs"
"Assets/Scripts/Enemy/Special/TentacleEnemy.cs"
"Assets/Scripts/Entity.cs"
"Assets/Scripts/EventHelper.cs"
"Assets/Scripts/ExtensionsStatic.cs"
"Assets/Scripts/Game/AreaTrigger.cs"
"Assets/Scripts/Game/AudioManager.cs"
"Assets/Scripts/Game/CameraController.cs"
"Assets/Scripts/Game/ChildMap.cs"
"Assets/Scripts/Game/DoorTrigger.cs"
"Assets/Scripts/Game/EffectPoolManager.cs"
"Assets/Scripts/Game/Entities/Birth/EntityBirth.cs"
"Assets/Scripts/Game/Entities/EnterNextLevel.cs"
"Assets/Scripts/Game/Entities/NPC/LevelNPC.cs"
"Assets/Scripts/Game/Entities/NPC/NPCBase.cs"
"Assets/Scripts/Game/Entities/NPC/ShopNPC.cs"
"Assets/Scripts/Game/EntitySpawnerManager.cs"
"Assets/Scripts/Game/MapMananger.cs"
"Assets/Scripts/Game/MusicManager.cs"
"Assets/Scripts/Game/NPCManager.cs"
"Assets/Scripts/Game/ProjectilesPool.cs"
"Assets/Scripts/Game/Room.cs"
"Assets/Scripts/Game/ScreenShark.cs"
"Assets/Scripts/Game/Service/MainService.cs"
"Assets/Scripts/Game/Service/ShopSpawnManager.cs"
"Assets/Scripts/Game/Service/WeaponService.cs"
"Assets/Scripts/Game/SpawnZone.cs"
"Assets/Scripts/Game/TipManager.cs"
"Assets/Scripts/GameManager.cs"
"Assets/Scripts/GameText.cs"
"Assets/Scripts/InitGame.cs"
"Assets/Scripts/Internal/Atributte/EnumAttribute.cs"
"Assets/Scripts/Internal/Atributte/vMinMaxAttribute.cs"
"Assets/Scripts/Internal/Enum/CardType.cs"
"Assets/Scripts/Internal/Enum/EntityType.cs"
"Assets/Scripts/Internal/Enum/PlayerProperty.cs"
"Assets/Scripts/Internal/Enum/WeaponProperty.cs"
"Assets/Scripts/Internal/General/MonoBehaviourBase.cs"
"Assets/Scripts/Internal/General/UpdateManager.cs"
"Assets/Scripts/LiveStats.cs"
"Assets/Scripts/Loading.cs"
"Assets/Scripts/MovementBehaviors/FollowPlayerMovementBehavior.cs"
"Assets/Scripts/MovementBehaviors/FollowRandPosAroundPlayerMovementBehavior.cs"
"Assets/Scripts/MovementBehaviors/MovementBehavior.cs"
"Assets/Scripts/MovementBehaviors/PatrolEdgesOfMapMovementBehavior.cs"
"Assets/Scripts/MovementBehaviors/StayInRangeFromPlayerMovementBehavior.cs"
"Assets/Scripts/MovementBehaviors/TargetRandPosAroundPlayerMovementBehavior.cs"
"Assets/Scripts/MovementBehaviors/TargetRandPosMovementBehavior.cs"
"Assets/Scripts/NetworkTime.cs"
"Assets/Scripts/OnTriggerEvent.cs"
"Assets/Scripts/OverLap/HitBox.cs"
"Assets/Scripts/OverLap/HurtBox.cs"
"Assets/Scripts/Particles/BurningParticles.cs"
"Assets/Scripts/Particles/ParticleEvent.cs"
"Assets/Scripts/PickUp/PickupItem.cs"
"Assets/Scripts/PickUp/PickupLoot.cs"
"Assets/Scripts/Player/AIPlayerController.cs"
"Assets/Scripts/Player/FirstPlayerController.cs"
"Assets/Scripts/Player/PlayerController.cs"
"Assets/Scripts/Player/PlayerInput.cs"
"Assets/Scripts/Player/PlayerReferences.cs"
"Assets/Scripts/PrivacyScene.cs"
"Assets/Scripts/Projectile/DelayedPlayerProjectile.cs"
"Assets/Scripts/Projectile/DelayedTaserProjectile.cs"
"Assets/Scripts/Projectile/EnemyProjectile.cs"
"Assets/Scripts/Projectile/EnemyProjectileRotating.cs"
"Assets/Scripts/Projectile/Pivot.cs"
"Assets/Scripts/Projectile/PlayerExplosion.cs"
"Assets/Scripts/Projectile/PlayerProjectile.cs"
"Assets/Scripts/Projectile/Projectile.cs"
"Assets/Scripts/Projectile/TaserProjectile.cs"
"Assets/Scripts/SceneMgr.cs"
"Assets/Scripts/ScrollRectNoDrag.cs"
"Assets/Scripts/Server/PlayerManager.cs"
"Assets/Scripts/Server/RandomNames.cs"
"Assets/Scripts/Server/SaveData.cs"
"Assets/Scripts/Server/ServerGameConfig.cs"
"Assets/Scripts/Structures/FlameTurret.cs"
"Assets/Scripts/Structures/Garden.cs"
"Assets/Scripts/Structures/LandMine.cs"
"Assets/Scripts/Structures/Neutral.cs"
"Assets/Scripts/Structures/Structure.cs"
"Assets/Scripts/Structures/Turret.cs"
"Assets/Scripts/Structures/WanderingBot.cs"
"Assets/Scripts/Tasks/MissionData_SO.cs"
"Assets/Scripts/Teach/PlayerPrefsX.cs"
"Assets/Scripts/Teach/TeachLogic.cs"
"Assets/Scripts/Teach/TeachManager.cs"
"Assets/Scripts/Teach/UITeach.cs"
"Assets/Scripts/Teach/UITeachItem.cs"
"Assets/Scripts/Test.cs"
"Assets/Scripts/TextService.cs"
"Assets/Scripts/UI/Business/ADBuyUI.cs"
"Assets/Scripts/UI/FixScreen.cs"
"Assets/Scripts/UI/Item/ClientMsgListener.cs"
"Assets/Scripts/UI/Item/MyGridLayoutGroup.cs"
"Assets/Scripts/UI/Item/OnTouchEvent.cs"
"Assets/Scripts/UI/Item/UISelectButton.cs"
"Assets/Scripts/UI/Item/UIToastItem.cs"
"Assets/Scripts/UI/Level/Card/ItemStatsPanel.cs"
"Assets/Scripts/UI/Level/DamageVignette.cs"
"Assets/Scripts/UI/Level/FrameRate.cs"
"Assets/Scripts/UI/Level/Item/ShopInGame/DraggableItemUI.cs"
"Assets/Scripts/UI/Level/Item/ShopInGame/ItemSlotUI.cs"
"Assets/Scripts/UI/Level/Item/ShopInGame/TrashAreaUI.cs"
"Assets/Scripts/UI/Level/Item/UICatalogItemBase.cs"
"Assets/Scripts/UI/Level/Item/UICatalogWeaponItem.cs"
"Assets/Scripts/UI/Level/Item/UIEquipItemBase.cs"
"Assets/Scripts/UI/Level/Item/UIItemBase.cs"
"Assets/Scripts/UI/Level/Item/UIMissionItem.cs"
"Assets/Scripts/UI/Level/Item/UISamllCatalogItem.cs"
"Assets/Scripts/UI/Level/Item/UIShowItemBase.cs"
"Assets/Scripts/UI/Level/Item/UIWarehouseItem.cs"
"Assets/Scripts/UI/Level/Item/WeaponSlotUI.cs"
"Assets/Scripts/UI/Level/UICardList.cs"
"Assets/Scripts/UI/Level/UIChoicePage.cs"
"Assets/Scripts/UI/Level/UIFollowerInfo.cs"
"Assets/Scripts/UI/Level/UIGameFinish.cs"
"Assets/Scripts/UI/Level/UIGameFollowerShop.cs"
"Assets/Scripts/UI/Level/UIGameReward.cs"
"Assets/Scripts/UI/Level/UIGameShop.cs"
"Assets/Scripts/UI/Level/UIMatchInfor.cs"
"Assets/Scripts/UI/Level/UIPlayerAttributes.cs"
"Assets/Scripts/UI/Level/UIPlayerBank.cs"
"Assets/Scripts/UI/Level/UISettlement.cs"
"Assets/Scripts/UI/Level/UIWarehouse.cs"
"Assets/Scripts/UI/Level/UIWeaponList.cs"
"Assets/Scripts/UI/Menu/LevelUp.cs"
"Assets/Scripts/UI/Menu/MenuCatalog.cs"
"Assets/Scripts/UI/Menu/MenuDailyTask.cs"
"Assets/Scripts/UI/Menu/MenuGame.cs"
"Assets/Scripts/UI/Menu/MenuGameOver.cs"
"Assets/Scripts/UI/Menu/MenuNewPlayerTask.cs"
"Assets/Scripts/UI/Menu/MenuPassLevelReward.cs"
"Assets/Scripts/UI/Menu/MenuPause.cs"
"Assets/Scripts/UI/Menu/MenuSelectProp.cs"
"Assets/Scripts/UI/Menu/MenuSettings.cs"
"Assets/Scripts/UI/Menu/MenuShop.cs"
"Assets/Scripts/UI/Menu/MenuSignin.cs"
"Assets/Scripts/UI/Menu/MenuSweep.cs"
"Assets/Scripts/UI/Menu/MenuUI.cs"
"Assets/Scripts/UI/Menu/MenuWeapon.cs"
"Assets/Scripts/UI/Menu/MenuWeaponInfo.cs"
"Assets/Scripts/UI/Menu/MixModeTips.cs"
"Assets/Scripts/UI/Menu/Navigation.cs"
"Assets/Scripts/UI/Menu/RealName.cs"
"Assets/Scripts/UI/Menu/ShowPlayer.cs"
"Assets/Scripts/UI/Menu/SingletonUI.cs"
"Assets/Scripts/UI/Menu/UIFeedBack.cs"
"Assets/Scripts/UI/Menu/UIManagers.cs"
"Assets/Scripts/UI/Menu/UIMenuBase.cs"
"Assets/Scripts/UI/Menu/UIMenuGetGoods.cs"
"Assets/Scripts/UI/Menu/UIMenuGift.cs"
"Assets/Scripts/UI/Menu/UIMenuToast.cs"
"Assets/Scripts/UI/Player/UIPlayerInfo.cs"
"Assets/Scripts/UI/Player/UIPlayerItem.cs"
"Assets/Scripts/UI/PlayerInput/UIJoystick.cs"
"Assets/Scripts/UI/Sound/ButtonClickSound.cs"
"Assets/Scripts/UI/Sound/UISFX.cs"
"Assets/Scripts/UI/Store/GamePass.cs"
"Assets/Scripts/UI/Store/PassItem.cs"
"Assets/Scripts/UI/Store/PassItemInfor.cs"
"Assets/Scripts/UI/UIProgressBar.cs"
"Assets/Scripts/Unit.cs"
"Assets/Scripts/Weapon/MeleeWeapon.cs"
"Assets/Scripts/Weapon/RangeWeapon.cs"
"Assets/Scripts/Weapon/ShootingBehaviors/MeleeWeaponShootingBehavior.cs"
"Assets/Scripts/Weapon/ShootingBehaviors/RangedWeaponShootingBehavior.cs"
"Assets/Scripts/Weapon/ShootingBehaviors/WeaponShootingBehavior.cs"
"Assets/Scripts/Weapon/WeaponBase.cs"
"Assets/Scripts/WX/OpenData.cs"
"Assets/Scripts/WX/WeChatRank.cs"
"Assets/Scripts/WX/WeChatTool.cs"
"Assets/Scripts/WX/WXReport.cs"
"Assets/Scripts/WX/WXShare.cs"
"Assets/SDK/Scripts/HuaWei/HuaWeiAccount.cs"
"Assets/SDK/Scripts/HuaWei/HWNativeMgr.cs"
"Assets/SDK/Scripts/HuaWei/LargeImageNative.cs"
"Assets/SDK/Scripts/SDKManager.cs"
"Assets/SDK/Scripts/Taptap/TapLogin.cs"
"Assets/SDK/Scripts/XMi/MiAccountInfo.cs"
"Assets/SDK/Scripts/XMi/MiSDKCallback.cs"
"Assets/SDK/Scripts/XMi/SDKAndroid.cs"
"Assets/SDK/Scripts/XMi/Singleton.cs"
"Assets/TalkingDataScripts/TalkingDataOrder.cs"
"Assets/TalkingDataScripts/TalkingDataProfile.cs"
"Assets/TalkingDataScripts/TalkingDataSDK.cs"
"Assets/TalkingDataScripts/TalkingDataSearch.cs"
"Assets/TalkingDataScripts/TalkingDataShoppingCart.cs"
"Assets/TalkingDataScripts/TalkingDataTransaction.cs"
"Assets/TalkingDataScripts/TDDemoScript.cs"
"Assets/UIEffect-upm/EffectExamples/Misc Effects/Scripts/SpawnEffect.cs"
-langversion:9.0

/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319

/nowarn:0169
/nowarn:0649
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US


/additionalfile:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.AdditionalFile.txt"