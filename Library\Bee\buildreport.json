{"instructions_readme": "1) Open Chrome, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "traceEvents": [{"pid": "Unity", "ph": "M", "name": "process_name", "args": {"name": "Unity"}}, {"pid": "Unity", "ph": "M", "name": "process_sort_index", "args": {"sort_index": -100}}, {"pid": "Unity", "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": "Unity", "tid": 1, "ts": 1748522737460150.0, "dur": 27187220, "ph": "X", "name": "Build player"}, {"pid": "Unity", "tid": 1, "ts": 1748522737493950.0, "dur": 38908, "ph": "X", "name": "Preprocess Player"}, {"pid": "Unity", "tid": 1, "ts": 1748522738038530.0, "dur": 26473727, "ph": "X", "name": "Prepare For Build"}, {}], "meta_datetime": "05/29/2025 20:46:04", "meta_command_line": "\"D:\\Program Files (x86)\\2021.3.21f1c1\\Editor\\Unity.exe\" -projectpath G:\\GAME_ZD\\Brotato_LegionSurvivors -useHub -hubIPC -cloudEnvironment production -buildTarget Android -licensingIpc LicenseClient-Administrator -hubSessionId f0b94480-3b63-11f0-8786-aff2046e9f93 -accessToken LJc5L3TCRMwh8HXKUl2rzEefNZVdhO28hxnHMo4xHTU01ef", "meta_command_line_args": "D:\\Program Files (x86)\\2021.3.21f1c1\\Editor\\Unity.exe -projectpath G:\\GAME_ZD\\Brotato_LegionSurvivors -useHub -hubIPC -cloudEnvironment production -buildTarget Android -licensingIpc LicenseClient-Administrator -hubSessionId f0b94480-3b63-11f0-8786-aff2046e9f93 -accessToken LJc5L3TCRMwh8HXKUl2rzEefNZVdhO28hxnHMo4xHTU01ef", "meta_user_name": "Administrator", "meta_os_version": "Microsoft Windows NT 10.0.19045.0", "meta_cpu_count": "12"}