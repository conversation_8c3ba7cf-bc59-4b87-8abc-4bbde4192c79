-assembly "Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll" -outputDir "Library/Bee/artifacts/1300b0aE.dag/post-processed" -assemblyReferences "Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll" -assemblyReferences "Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll" -assemblyReferences "Assets/Editor/Beebyte/Obfuscator/Mono.Cecil/Beebyte.Cecil.dll" -assemblyReferences "Assets/Editor/Beebyte/Obfuscator/Mono.Cecil/Beebyte.Cecil.Mdb.dll" -assemblyReferences "Assets/Editor/Beebyte/Obfuscator/Mono.Cecil/Beebyte.Cecil.Pdb.dll" -assemblyReferences "Assets/Editor/Beebyte/Obfuscator/Mono.Cecil/Beebyte.Cecil.Rocks.dll" -assemblyReferences "Assets/Editor/Beebyte/Obfuscator/Plugins/obfuscator.dll" -assemblyReferences "Assets/Editor/EPPlus/EPPlus.dll" -assemblyReferences "Assets/I2/Localization/Scripts/Editor/Unity XCode/UnityEditor.iOS_I2Loc.Xcode.dll" -assemblyReferences "Assets/Plugins/Demigiant/DOTween/DOTween.dll" -assemblyReferences "Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll" -assemblyReferences "Assets/Plugins/Excel.dll" -assemblyReferences "Assets/Plugins/ICSharpCode.SharpZipLib.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEditor.Graphs.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIWidgetsModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll" -assemblyReferences "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll" -assemblyReferences "G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll" -assemblyReferences "G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll" -assemblyReferences "G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll" -assemblyReferences "G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll" -assemblyReferences "G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.Unsafe.dll" -processors "Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.CodeGen.dll" -defines "UNITY_2021_3_21" -defines "UNITY_2021_3" -defines "UNITY_2021" -defines "UNITY_5_3_OR_NEWER" -defines "UNITY_5_4_OR_NEWER" -defines "UNITY_5_5_OR_NEWER" -defines "UNITY_5_6_OR_NEWER" -defines "UNITY_2017_1_OR_NEWER" -defines "UNITY_2017_2_OR_NEWER" -defines "UNITY_2017_3_OR_NEWER" -defines "UNITY_2017_4_OR_NEWER" -defines "UNITY_2018_1_OR_NEWER" -defines "UNITY_2018_2_OR_NEWER" -defines "UNITY_2018_3_OR_NEWER" -defines "UNITY_2018_4_OR_NEWER" -defines "UNITY_2019_1_OR_NEWER" -defines "UNITY_2019_2_OR_NEWER" -defines "UNITY_2019_3_OR_NEWER" -defines "UNITY_2019_4_OR_NEWER" -defines "UNITY_2020_1_OR_NEWER" -defines "UNITY_2020_2_OR_NEWER" -defines "UNITY_2020_3_OR_NEWER" -defines "UNITY_2021_1_OR_NEWER" -defines "UNITY_2021_2_OR_NEWER" -defines "UNITY_2021_3_OR_NEWER" -defines "UNITY_INCLUDE_TESTS" -defines "ENABLE_AR" -defines "ENABLE_AUDIO" -defines "ENABLE_CACHING" -defines "ENABLE_CLOTH" -defines "ENABLE_EVENT_QUEUE" -defines "ENABLE_MICROPHONE" -defines "ENABLE_MULTIPLE_DISPLAYS" -defines "ENABLE_PHYSICS" -defines "ENABLE_TEXTURE_STREAMING" -defines "ENABLE_UNET" -defines "ENABLE_LZMA" -defines "ENABLE_UNITYEVENTS" -defines "ENABLE_VR" -defines "ENABLE_WEBCAM" -defines "ENABLE_UNITYWEBREQUEST" -defines "ENABLE_WWW" -defines "ENABLE_CLOUD_SERVICES" -defines "ENABLE_CLOUD_SERVICES_COLLAB" -defines "ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS" -defines "ENABLE_CLOUD_SERVICES_ADS" -defines "ENABLE_CLOUD_SERVICES_USE_WEBREQUEST" -defines "ENABLE_CLOUD_SERVICES_CRASH_REPORTING" -defines "ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING" -defines "ENABLE_CLOUD_SERVICES_PURCHASING" -defines "ENABLE_CLOUD_SERVICES_ANALYTICS" -defines "ENABLE_CLOUD_SERVICES_UNET" -defines "ENABLE_CLOUD_SERVICES_BUILD" -defines "ENABLE_CLOUD_LICENSE" -defines "ENABLE_EDITOR_HUB_LICENSE" -defines "ENABLE_WEBSOCKET_CLIENT" -defines "ENABLE_DIRECTOR_AUDIO" -defines "ENABLE_DIRECTOR_TEXTURE" -defines "ENABLE_MANAGED_JOBS" -defines "ENABLE_MANAGED_TRANSFORM_JOBS" -defines "ENABLE_MANAGED_ANIMATION_JOBS" -defines "ENABLE_MANAGED_AUDIO_JOBS" -defines "ENABLE_RUNTIME_PERMISSIONS" -defines "ENABLE_ENGINE_CODE_STRIPPING" -defines "ENABLE_ONSCREEN_KEYBOARD" -defines "ENABLE_MANAGED_UNITYTLS" -defines "INCLUDE_DYNAMIC_GI" -defines "ENABLE_SCRIPTING_GC_WBARRIERS" -defines "PLATFORM_SUPPORTS_MONO" -defines "ENABLE_VIDEO" -defines "ENABLE_ACCELERATOR_CLIENT_DEBUGGING" -defines "PLATFORM_ANDROID" -defines "TEXTCORE_1_0_OR_NEWER" -defines "UNITY_ANDROID" -defines "UNITY_ANDROID_API" -defines "ENABLE_EGL" -defines "ENABLE_NETWORK" -defines "ENABLE_RUNTIME_GI" -defines "ENABLE_CRUNCH_TEXTURE_COMPRESSION" -defines "UNITY_CAN_SHOW_SPLASH_SCREEN" -defines "UNITY_HAS_GOOGLEVR" -defines "UNITY_HAS_TANGO" -defines "ENABLE_SPATIALTRACKING" -defines "ENABLE_ETC_COMPRESSION" -defines "PLATFORM_EXTENDS_VULKAN_DEVICE" -defines "PLATFORM_HAS_MULTIPLE_SWAPCHAINS" -defines "UNITY_ANDROID_SUPPORTS_SHADOWFILES" -defines "PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP" -defines "ENABLE_UNITYADS_RUNTIME" -defines "UNITY_UNITYADS_API" -defines "ENABLE_MONO" -defines "NET_STANDARD_2_0" -defines "NET_STANDARD" -defines "NET_STANDARD_2_1" -defines "NETSTANDARD" -defines "NETSTANDARD2_1" -defines "ENABLE_PROFILER" -defines "DEBUG" -defines "TRACE" -defines "UNITY_ASSERTIONS" -defines "UNITY_EDITOR" -defines "UNITY_EDITOR_IG" -defines "UNITY_EDITOR_64" -defines "UNITY_EDITOR_WIN" -defines "ENABLE_UNITY_COLLECTIONS_CHECKS" -defines "ENABLE_BURST_AOT" -defines "UNITY_TEAM_LICENSE" -defines "ENABLE_CUSTOM_RENDER_TEXTURE" -defines "ENABLE_DIRECTOR" -defines "ENABLE_LOCALIZATION" -defines "ENABLE_SPRITES" -defines "ENABLE_TERRAIN" -defines "ENABLE_TILEMAP" -defines "ENABLE_TIMELINE" -defines "ENABLE_LEGACY_INPUT_MANAGER" -defines "TEXTCORE_FONT_ENGINE_1_5_OR_NEWER" -defines "TextMeshPro" -defines "Vertical_Screen" -defines "TIMELINE_FRAMEACCURATE" -defines "CSHARP_7_OR_LATER" -defines "CSHARP_7_3_OR_NEWER" -assemblyFolders "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed" -assemblyFolders "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/Managed/UnityEngine" -assemblyFolders "Assets/Editor/Beebyte/Obfuscator/Mono.Cecil" -assemblyFolders "Assets/Editor/Beebyte/Obfuscator/Plugins" -assemblyFolders "Assets/Editor/EPPlus" -assemblyFolders "Assets/I2/Localization/Scripts/Editor/Unity XCode" -assemblyFolders "Assets/Plugins" -assemblyFolders "Assets/Plugins/Demigiant/DOTween" -assemblyFolders "Assets/Plugins/Demigiant/DOTween/Editor" -assemblyFolders "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx" -assemblyFolders "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard" -assemblyFolders "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/Extensions/2.0.0" -assemblyFolders "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/NetStandard/ref/2.1.0" -assemblyFolders "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/AndroidPlayer" -assemblyFolders "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" -assemblyFolders "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api" -assemblyFolders "D:/Program Files (x86)/2021.3.21f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades" -assemblyFolders "G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6" -assemblyFolders "G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.burst@1.6.6/Unity.Burst.CodeGen" -assemblyFolders "G:/GAME_ZD/Brotato_LegionSurvivors/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom" -assemblyFolders "Library/Bee/artifacts/1300b0aE.dag"